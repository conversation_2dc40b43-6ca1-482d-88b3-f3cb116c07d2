import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>d, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import {
  MatTableDataSource,
  MatPaginator,
  MatSort,
  MatDialog,
  MatSelect,
  MatOption,
} from "@angular/material";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { JobsService } from "src/app/Apiservices/jobs/jobs.service";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { JobRequests } from "src/app/models/JobRequests";
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
} from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import * as _moment from "moment";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import * as moment from "moment";
import { ReplaySubject, Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { AssignJobComponent } from "../job-requests/list-job-requests/assign-job/assign-job.component";
import { ActivatedRoute, Router } from "@angular/router";
import { BaseTimerComponent } from "src/app/shared-theme/base-timer/base-timer.component";
declare var $: any;

const staticColumn = {
  color_code: "color_code",
  "Job No": "order_no",
  Creation: "request_time",
  From: "from_location",
  To: "to_location",
  Task: "task",
  "Patient Info": "patient_name",
  "Porter Name": "porter_name",
  Actions: "order_id",
  "Cancel reason": "cancel_reason",
  "Delay reason": "delay_reason",
  "Assigned time": "assign_time",
  "Cancel time": "cancellation_time",
  "Responded time": "start_time",
  "Completion time": "completion_time",
};

const displayLegends = {
  "In theQueue": "Queue",
  Responded: "Responded",
  Cancelled: "Cancelled",
  Completed: "Completed",
  Advance: "Advance",
  Assigned: "Assigned",
};

@Component({
  selector: "app-view-status-by-location",
  templateUrl: "./view-status-by-location.component.html",
  styleUrls: [
    "../../scss/table.scss",
    "./view-status-by-location.component.scss",
  ],
})
export class ViewStatusByLocationComponent implements OnInit, OnDestroy {
  @ViewChild(BaseTimerComponent, { static: true }) baseTimer: BaseTimerComponent;
  allowReamrks = false;
  actionStatus = { value: "INQ", color_code: "" };
  displayedColumns: string[] = [
    "order_no",
    "request_time",
    "from_location",
    "to_location",
    "task",
    "patient_name",
    "porter_name",
    "order_id",
  ];
  pdfData = [];
  colorCodes = [];
  filter = "";
  isEditDelayReason = false;
  reasons = [];
  delayReasonsofJob = [];
  editableDelayReasinId = "";
  staffs = [];
  staffStatus: any = "";
  toatlJobsStaff: any;
  imageUrl: any;
  reCalldata: any;
  hoveredJob: any;
  actionForm: FormGroup;
  jobNumber = { order_id: "", actionType: "", order_no: "" };
  dataSource: MatTableDataSource<JobRequests> = new MatTableDataSource([]);
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  todaydate = moment().format("DD/MM/YYYY HH:mm");
  loggedInUserId: string;
  isButtonList: any[] = [];
  jobData = [];
  taskRequest = {
    patient: null,
    location_id: null,
    date: "",
    job_category: null,
  };
  location_id = [];
  locationsList: any = [];
  loc = [];
  skillSet: any;
  selectedCode: { code: string; condition: string } = {
    code: "",
    condition: "",
  };
  length = 0;
  pageSize = 0;
  selected: any;
  protected _onDestroy = new Subject<void>();
  public filteredStaffs: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public staffFilterCtrl: FormControl = new FormControl();
  public filteredLocations: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public locationFilterCtrl: FormControl = new FormControl();
  @ViewChild(MatOption, { static: true }) allSelected: MatOption;
  @ViewChild(MatSelect, { static: true }) select: MatSelect
  constructor(
    private readonly jobService: JobsService,
    private readonly loader: LoaderService,
    private readonly fb: FormBuilder,
    private readonly toastr: ToastrService,
    private readonly userService: UserService,
    public facilityConfig: FacilityConfigService,
    private dialog: MatDialog,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
    this.createActionForm();
    this.loggedInUserId = atob(localStorage.getItem("user_id"));
  }

  filterLocations() {
    if (!this.locationsList) {
      return;
    }
    // get the search keyword
    let search = this.locationFilterCtrl.value;
    // DON'T REMOVE THIS CODE
    // this.taskRequest.fromLocationId =
    //   this.locations.length > 1 ? this.locations.slice()[0].id : "";
    if (!search) {
      this.filteredLocations.next(<any[]>this.locationsList.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredLocations.next(
      this.locationsList.filter(
        (x) => x.locationName.toLowerCase().indexOf(search) > -1
      )
    );
  }
  createActionForm() {
    this.actionForm = this.fb.group({
      reason: ["", Validators.required],
      time: [{ value: moment().format("DD/MM/YYYY HH:mm"), disabled: true }],
      staff: ["", Validators.required],
      remarks: [""],
    });
    this.imageUrl = null;
    this.staffStatus = "";
    this.toatlJobsStaff = 0;
    this.allowReamrks = false;
  }

  ngOnInit() {
    this.getCurrentDateTime();
    this.fetchGridListData();
    // this.getjobRequestsById();
    this.getLocations();
    if (this.actionStatus.value === "In theQueue") {
      this.actionStatus.value = "INQ";
    }

    // listen for search field value changes
    this.staffFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterStaff();
      });
    this.getColorCodes();

    // real time search location
    this.filteredLocations.next(this.locationsList.slice());
    this.locationFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterLocations();
      });
  }

  callThirtySecFun() {
    this.resetData()
    this.getjobRequests(
      this.actionStatus.value !== "In theQueue"
        ? this.actionStatus.value
        : "INQ"
    );
  }

  tosslePerOne(all) {
    if (this.allSelected.selected) {
      this.allSelected.deselect();
      return false;
    }
    if (this.location_id.length == this.locationsList.length) {
      this.allSelected.select();
    }
    this.getjobRequestsById();
  }
  toggleAllSelection() {
    //  this.allSelected.selected || 
    if (this.selected.includes('All')) {
      this.locationsList.map(item => {
        this.location_id[0] = 'All';
        this.location_id.push(item.locationName);
        this.select.options.forEach((item: MatOption) => item.select());
      })
    } else {
      this.location_id = [];
      this.select.options.forEach((item: MatOption) => item.deselect());
    }
    this.getjobRequestsById();
  }

  protected filterStaff() {
    if (!this.staffs) {
      return;
    }
    // get the search keyword
    let search = this.staffFilterCtrl.value;
    if (!search) {
      this.filteredStaffs.next(this.staffs.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the staffs
    this.filteredStaffs.next(
      this.staffs.filter(
        (staff) => staff.staff_name.toLowerCase().indexOf(search) > -1
      )
    );
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.todaydate = res;
      this.createActionForm();
    });
  }

  getStaff() {
    this.staffs = [];
    this.jobService.getAssignstaff().subscribe((res) => {
      this.staffs = res || [];
      this.filteredStaffs.next(this.staffs.slice());
    });
  }

  openDialogAction() {
    const dialogRef = this.dialog.open(AssignJobComponent, {
      width: "40%",
      height: "auto",
      disableClose: true,
      data: { jobNumber: this.jobNumber },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.getjobRequestsById();
        this.getColorCodes();
        this.baseTimer.start();
      }else{
        this.baseTimer.start();
      }
    });
  }

  getReasons(reason) {
    this.reasons = [];
    this.jobService
      .getReasons(
        reason === "cancel"
          ? "api/viewstatus/cancel/reason"
          : "api/viewstatus/delayreasons"
      )
      .subscribe((res) => {
        res =
          res &&
          res.filter((vals) => {
            if (vals.status) {
              return vals;
            }
          });
        this.reasons = res || [];
      });
  }

  viewJobDetailsHover(data) {
    this.hoveredJob = data;
  }

  getDelayReasons(jobNumber) {
    this.delayReasonsofJob = [];
    if (jobNumber) {
      this.jobService.getDelayReasonsForJob(jobNumber).subscribe((res) => {
        this.delayReasonsofJob = res || [];
      });
    }
  }

  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "jobrequesttable",
        "jobrequesttable"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("jobrequesttable", "jobrequesttable");
    }
  }

  getColorCodes() {
    const data = {
      "locations": this.location_id.length > 0 ? this.loc : this.location_id,
      'filterType': 2
    }
    this.jobService
      .getColorCodes(data)
      .subscribe((res) => {
        res =
          res &&
          res
            .map((status) => this.getDisplayName(status))
            .filter((val) => {
              // tslint:disable-next-line:no-string-literal
              val["statusCode"] = this.getStatusCode(val.condition);
              if ((this.actionStatus.value == 'INQ' && val.condition == 'In theQueue') || (this.actionStatus.value == val.condition)) {
                this.length = val.count
              }
              return val;
            });
        this.colorCodes = res || [];
        if (this.actionStatus.value === "INQ") {
          this.actionStatus.value = "In theQueue";
        }
        const code = this.actionStatus.value
          ? this.actionStatus.value
          : "In theQueue";
        const isCodeExist = this.colorCodes.find(
          (color) => color.condition === code
        );
        if (isCodeExist) {
          this.actionStatus.color_code = isCodeExist.color_code;
        }
        if (this.colorCodes.length) {
          this.selectedCode.code = this.colorCodes[0].code;
          this.selectedCode.condition = this.colorCodes[0].condition;
        }

        if (code) {
          this.colorCodes = this.colorCodes.map((data) => {
            return data.condition.includes(code.trim())
              ? { ...data, show_active: true }
              : data;
          });
        } else {
          this.colorCodes = this.colorCodes.map((data) =>
            data.condition == "In theQueue"
              ? { ...data, show_active: true }
              : data
          );
        }
      });
  }

  getjobRequests(code?, condition?, nextPageData?) {
    // make legend active
    // this.colorCodes = this.colorCodes.map((data) => {
    //   console.log(data.condition == condition, data.condition, condition);
    //   if (data.condition == condition) {
    //     return { ...data, show_active: true };
    //   } else {
    //     return {
    //       ...data,
    //       show_active: false,
    //       background: "#FFFFFF",
    //       color: "#000000",
    //     };
    //   }
    // });
    this.fetchGridListData();
    if (!nextPageData) {
      this.paginator.pageIndex = 0;
    }
    if (this.colorCodes.length) {
      this.colorCodes.filter(val => {
        if ((this.actionStatus.value == 'INQ' && val.condition == 'In theQueue') ||
          (this.actionStatus.value.includes('Queue') && val.condition == 'In theQueue') ||
          (this.actionStatus.value.includes(val.condition))) {
          this.length = val.count
        }
      })
    }
    this.selectedCode.code = code;
    this.selectedCode.condition = condition == 'next' ? code : condition;
    this.filter = "";
    this.dataSource = new MatTableDataSource([]);
    this.baseTimer.stopTimerForDataLoadFun(true);
    code = code === "Responded" ? "Started" : code == undefined || code.includes('Queue') ? 'INQ' : code;
    const data = {
      'jobStatus': code || "INQ",
      // 'locationId': this.location_id,
      'locations': this.location_id.length > 0 ? this.loc : this.location_id,
      'filterType': 2,
      'pageNo': nextPageData ? Number(nextPageData.offset) + 1 : 1,
      'pageSize': nextPageData ? Number(nextPageData.limit) : 50
    }
    this.getColorCodes();
    this.jobService
      .getJobRequests(data, this.location_id, "locationId")
      .subscribe(
        (res) => {
          if (res && res.length > 0) {
            this.actionStatus.value =
              res[0].job_status === "In Queue"
                ? "In theQueue"
                : res[0].job_status === "Started"
                  ? "Responded"
                  : res[0].job_status;
            this.jobData = res;
            if (nextPageData) {
              this.jobData.length = nextPageData.currentSize == 0 ? res[0].full_count : nextPageData.currentSize;
              this.jobData.push(...res);
              this.jobData.length = res[0].full_count;
              this.dataSource = new MatTableDataSource(
                res
                  ? res.map((data) => ({
                    ...data,
                    transport_mode_image: data && data.transport_mode_image && data.transport_mode_image !== null ? JSON.parse(data.transport_mode_image) : '',
                  }))
                  : []
              );
              this.dataSource._updateChangeSubscription();
              this.dataSource.paginator = this.paginator;
              this.baseTimer.stopTimerForDataLoadFun(false);
            } else {
              this.jobData.length = res[0].full_count;
              this.dataSource = new MatTableDataSource(
                res
                  ? res.map((data) => ({
                    ...data,
                    transport_mode_image: data && data.transport_mode_image && data.transport_mode_image !== null ? JSON.parse(data.transport_mode_image) : '',
                  }))
                  : []
              );
              this.dataSource.sort = this.sort;
              this.dataSource.paginator = this.paginator;
              this.baseTimer.stopTimerForDataLoadFun(false);
            }
            this.setPageSizeOptions();
            this.getColorCodes();
          } else {
            this.paginator.length = 0;
            this.dataSource = new MatTableDataSource([]);
            this.baseTimer.stopTimerForDataLoadFun(false);
          }
        },
        () => {
          this.paginator.length = 0;
          this.dataSource = new MatTableDataSource([]);
          this.baseTimer.stopTimerForDataLoadFun(false);
        }
      );
  }

  pageChanged(event) {
    let pageIndex = event.pageIndex;
    let pageSize = event.pageSize;
    let previousIndex = event.previousPageIndex;
    let previousSize = pageSize * pageIndex;
    this.baseTimer.reset();
    this.baseTimer.start();
    this.getNextData(previousSize, (pageIndex).toString(), pageSize.toString());
  }

  getNextData(currentSize, offset, limit) {
    const paginationData = {
      'currentSize': currentSize,
      'offset': offset,
      'limit': limit
    }
    this.getjobRequests(this.actionStatus.value, 'next', paginationData)
  }

  getjobRequestsById() {
    this.getjobRequests(
      this.actionStatus.value !== "In theQueue"
        ? this.actionStatus.value
        : "INQ"
    );
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Job No": key.order_no ? key.order_no : "--",
              Creation: key.request_time ? key.request_time : "--",
              From: key.from_location ? key.from_location : "--",
              To: key.to_location ? key.to_location : "--",
              Equipment: key.transport_mode_name
                ? key.transport_mode_name
                : "--",
              Task: key.task ? key.task : "--",
              "Patient Info": key.patient_name ? key.patient_name : "--",
              "Porter Name": key.porter_name ? key.porter_name : "--",
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  getStatusCode(code) {
    return {
      "In the Queue": "INQ",
      Responded: "Started",
      Cancelled: "Cancelled",
      Completed: "Completed",
      Advance: "Advance",
      Assigned: "Assigned",
    }[code];
  }
  getDisplayName(code) {
    return {
      ...code,
      display: displayLegends.hasOwnProperty(code.condition)
        ? displayLegends[code.condition]
        : code.condition,
    };
  }

  staffChanged(event) {
    if (event) {
      this.jobService.getStaffForJobrequestbyid(event).subscribe((res) => {
        this.staffStatus = res.status ? res.status : "0 Job(s) in hand";
        this.toatlJobsStaff = res.total_jobs;
        this.skillSet = res.skillset;

        if (res.staff_photo) {
          this.userService.downloadImages(res.staff_photo).subscribe((img) => {
            const blob = new Blob([img.body], {
              type: "image/png",
            });
            const oFReader = new FileReader();
            oFReader.readAsDataURL(blob);
            oFReader.onload = (oFREvent) => {
              this.imageUrl = oFREvent && oFREvent.target;
            };
          });
        }
      });
    }
  }

  actionTaken() {
    switch (this.jobNumber.actionType) {
      case "Assign":
        this.assignReassign("assign");
        break;
      case "Cancel":
        if (this.actionForm.get("reason").valid) {
          const x = this.actionForm.value;
          this.jobService
            .takeAction(`api/viewstatus/cancel/${this.jobNumber.order_id}`, x)
            .subscribe(
              () => {
                this.toastr.success("Job cancelled successfully", "Success");
                this.actionFormSubmiited();
              },
              (err) => {
                console.log(err);
              }
            );
        } else {
          this.toastr.warning(
            "Please enter all highlighted fields",
            "Validation failed!"
          );
          this.actionForm.markAllAsTouched();
        }
        break;
      case "Respond":
        this.startComplete("start", "startdate");
        break;
      case "Reassign":
        this.assignReassign("reassign");
        break;
      case "Delay Reason":
        if (this.actionForm.get("reason").valid) {
          const x = Object.assign({}, this.actionForm.value);
          const y = {
            job_no: this.jobNumber.order_no,
            delay_reason: x.reason,
            remark: x.remarks,
          };
          this.jobService
            .jobRequest(
              y,
              this.isEditDelayReason
                ? `api/viewstatus/delayreason/edit/${this.editableDelayReasinId}`
                : `api/viewstatus/delayreason/add`,
              this.isEditDelayReason ? "up" : "add"
            )
            .subscribe(
              () => {
                this.toastr.success(
                  "Delay reason added successfully",
                  "Success"
                );
                this.actionFormSubmiited();
              },
              (err) => {
                console.log(err);
              }
            );
        } else {
          this.toastr.warning(
            "Please enter all highlighted fields",
            "Validation failed!"
          );
          this.actionForm.markAllAsTouched();
        }
        break;
      case "Complete":
        this.startComplete("complete", "completion_date");
        break;
      case "Return Equipment":
        this.jobService
          .takeAction(
            `api/viewstatus/returnEquipment/${this.jobNumber.order_id}`
          )
          .subscribe(
            () => {
              this.toastr.success("Equipment returned successfully", "Success");
              this.actionFormSubmiited();
            },
            (err) => {
              console.log(err);
            }
          );
        break;
      case "Fetchback":
        this.jobService
          .takeAction(`api/viewstatus/fetchback/${this.jobNumber.order_no}`)
          .subscribe(
            () => {
              this.toastr.success("Fetched back successfully", "Success");
              this.actionFormSubmiited();
            },
            (err) => {
              console.log(err);
            }
          );
        break;
      default:
        this.actionFormSubmiited();
    }
  }

  startComplete(action, actionType) {
    const x = this.todaydate;
    const formattedDate = _moment(x).format("YYYY-MM-DD HH:mm ");
    this.jobService
      .takeAction(
        `api/viewstatus/${action}/${this.jobNumber.order_id}?${actionType}=${formattedDate}`
      )
      .subscribe(
        () => {
          this.toastr.success(`Job ${action}ed successfully`, "Success");
          this.actionFormSubmiited();
        },
        (err) => {
          console.log(err);
        }
      );
  }

  cancelReasonChange(reason) {
    if (reason) {
      const exist = this.reasons.find(
        (resn) => resn.cancel_template_id === Number(reason)
      );
      if (exist) {
        this.allowReamrks = exist.allow_remarks;
      }
      const existdelay = this.reasons.find(
        (resn) => resn.delay_id === Number(reason)
      );
      if (existdelay) {
        this.allowReamrks = existdelay.allow_remarks;
      }
    }
  }

  assignReassign(action) {
    if (this.actionForm.get("staff").valid) {
      const x = this.actionForm.get("staff").value;
      this.jobService
        .takeAction(
          `api/viewstatus/${action}/${this.jobNumber.order_id}?porterid=${x}`
        )
        .subscribe(
          () => {
            this.toastr.success(`Job ${action}ed successfully`, "Success");
            this.actionFormSubmiited();
          },
          (err) => {
            console.log(err);
          }
        );
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.actionForm.markAllAsTouched();
    }
  }

  editDelayReason(data) {
    this.isEditDelayReason = true;
    this.editableDelayReasinId = data.delay_rsn_id;
    this.actionForm.get("reason").setValue(Number(data.delay_reason));
    this.actionForm.get("remarks").setValue(data.remark);
  }

  actionFormSubmiited() {
    if (this.reCalldata) {
      clearInterval(this.reCalldata);
    }
    this.ngOnInit();
    this.actionForm.reset();
    $("#actionModal").modal("hide");
    this.createActionForm();
    this.isEditDelayReason = false;
    this.editableDelayReasinId = "";
    this.imageUrl = null;
    this.staffStatus = "";
    this.toatlJobsStaff = 0;
    this.allowReamrks = false;
  }

  dateStructure(date) {
    return date.replace("T", " ");
  }

  fetchGridListData() {
    let displayFormattedCols;
    this.userService
      .getViewStatusGridsByUser(this.loggedInUserId)
      .subscribe((column) => {
        this.displayedColumns = [
          ...column
            .sort((a, b) => a.order - b.order)
            .filter((data) => staticColumn[data.grid_name])
            .map((data) => staticColumn[data.grid_name]),
        ];
        if (this.actionStatus.value) {
          if (this.actionStatus.value == 'Advance' || this.actionStatus.value == 'INQ' || this.actionStatus.value.includes('Queue')) {
            displayFormattedCols = this.displayedColumns.filter(col => (col !== 'porter_name' && col !== 'assign_time' && col !== 'start_time' &&
              col !== 'completion_time' && col !== 'cancellation_time' && col !== 'delay_reason' && col !== 'cancel_reason'))
          } else if (this.actionStatus.value == 'Assigned') {
            displayFormattedCols = this.displayedColumns.filter(col => (col !== 'start_time' &&
              col !== 'completion_time' && col !== 'cancellation_time' && col !== 'cancel_reason'))
          } else if (this.actionStatus.value == 'Responded' || this.actionStatus.value == 'Started') {
            displayFormattedCols = this.displayedColumns.filter(col => (col !== 'completion_time' && col !== 'cancellation_time' && col !== 'cancel_reason'))
          } else if (this.actionStatus.value == 'Cancelled') {
            displayFormattedCols = this.displayedColumns.filter(col => (col !== 'completion_time'))
          } else if (this.actionStatus.value == 'Completed') {
            displayFormattedCols = this.displayedColumns.filter(col => (col !== 'cancellation_time' && col !== 'cancel_reason'))
          }
          this.displayedColumns = displayFormattedCols || this.displayedColumns;
        } else {
          displayFormattedCols = this.displayedColumns.filter(col => (col !== 'porter_name' && col !== 'assign_time' && col !== 'start_time' &&
            col !== 'completion_time' && col !== 'cancellation_time' && col !== 'delay_reason' && col !== 'cancel_reason'))
          this.displayedColumns = displayFormattedCols
        }
        this.isButtonList = column
          .filter((data) => data.is_btn)
          .map((data) => data.grid_name);
      });
  }

  getLocations() {
    // const allLoc = {'locationId': '', 'locationName': 'All'};
    // this.locationsList.push(allLoc);
    this.location_id = ['All'];
    this.userService.getLocations().subscribe((res) => {
      // res =
      //   (res &&
      //     res.length &&
      //     res.filter((val) => {
      //       if (val.status) {
      //         return val;
      //       }
      //     })) ||
      //   [];
      res.filter(data => {
        this.locationsList.push(data);
        this.loc.push(data.locationId)
        this.location_id.push(data.locationId);
      })
      // this.locationsList = res;
      this.getjobRequestsById();
      this.filterLocations();
    });
  }

  hoverLegendColor(colorCode, colorCodeJSON, textColor) {
    colorCodeJSON.background = colorCode;
    colorCodeJSON.color = textColor;
  }

  clearFormValue(formField: string) {
    this.taskRequest[formField] = "";
  }

  resetData() {
    this.pageSize = 50;
    this.paginator.pageSize = 50;
    this.dataSource.paginator = this.paginator;
    this.dataSource.paginator.pageSize = 50;
  }

  ngOnDestroy() {
    $("#actionModal").modal("hide");
    if (this.reCalldata) {
      clearInterval(this.reCalldata);
    }
  }

  gotoCreateJob() {
    this.router.navigate([`./addjobrequest`, { 'page': 'location' }], { relativeTo: this.activatedRoute })
  }

  updateJob(data) {
    this.router.navigate([`./updatejobrequest/${data.order_id}`, { 'type': 'recreate', 'status': data.job_status }], { relativeTo: this.activatedRoute })
  }
}
