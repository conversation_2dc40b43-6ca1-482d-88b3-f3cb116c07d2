import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { SinglePageComponentRoutingModule } from "./single-page-component-routing.module";
import { ListAdtPatientlogComponent } from "./list-adt-patientlog/list-adt-patientlog.component";
import { DependencyModule } from "src/app/dependency/dependency.module";
import { SharedThemeModule } from "src/app/shared-theme/shared-theme.module";
import { SharedModule } from "src/app/shared/shared.module";
import { UpdateFeedbackEmailComponent } from "./update-feedback-email/update-feedback-email.component";
import { ListOngoingBusrouteComponent } from "./list-ongoing-busroute/list-ongoing-busroute.component";
import { ListEnhancedReportComponent } from "./list-enhanced-report/list-enhanced-report.component";
import { ListViewFeedbackComponent } from "./list-view-feedback/list-view-feedback.component";
import { PorterSummaryReportComponent } from "./porter-summary-report/porter-summary-report.component";
import { BusRouteSearchComponent } from "./bus-route-search/bus-route-search.component";
import { EquipmentMoveReportComponent } from "./equipment-move-report/equipment-move-report.component";
import { TaskTypeSummaryComponent } from "./task-type-summary/task-type-summary.component";
import { LocationReportComponent } from "./location-report/location-report.component";
import { HourlySummaryReportComponent } from "./hourly-summary-report/hourly-summary-report.component";
import { KpiSummaryReportComponent } from "./kpi-summary-report/kpi-summary-report.component";
import { ListStationPorterComponent } from "./station-porter-report/list-station-porter/list-station-porter.component";
// tslint:disable-next-line: max-line-length
import { ListStationSpecificReportComponent } from "./station-specific-report/list-station-specific-report/list-station-specific-report.component";
import { AdSettingsComponent } from "./ad-settings/ad-settings.component";
import { SinglePageComponent } from "./single-page.component";
import { UamReportComponent } from "./uam-report/uam-report.component";
import { RoleComponent } from "./role/role.component";
import { UsersReportComponent } from "./users-report/users-report.component";
import { DisabledUsersComponent } from "./disabled-users/disabled-users.component";
import { ActiveUsersComponent } from "./active-users/active-users.component";
import { MatToolbarModule } from "@angular/material/toolbar";
import { MatTabsModule } from "@angular/material/tabs";
import { NgxMatSelectSearchModule } from "ngx-mat-select-search";
import { MonthlyrawReportComponent } from './monthlyraw-report/monthlyraw-report.component';
import { PtsReportsComponent } from './pts-reports/pts-reports.component';
import { EnhancedLocationReportComponent } from './enhanced-location-report/enhanced-location-report.component';
import { MessagesReportComponent } from './messages-report/messages-report.component';
import { RtlsMessagesReportComponent } from './rtls-messages-report/rtls-messages-report.component';
import { UamStaffReportComponent } from './uam-staff-report/uam-staff-report.component';
import { UamReportMasterExportComponent } from './uam-report-master-export/uam-report-master-export.component';

@NgModule({
  declarations: [
    SinglePageComponent,
    ListAdtPatientlogComponent,
    UpdateFeedbackEmailComponent,
    ListOngoingBusrouteComponent,
    ListViewFeedbackComponent,
    ListEnhancedReportComponent,
    BusRouteSearchComponent,
    LocationReportComponent,
    PorterSummaryReportComponent,
    EquipmentMoveReportComponent,
    KpiSummaryReportComponent,
    TaskTypeSummaryComponent,
    HourlySummaryReportComponent,
    ListStationPorterComponent,
    ListStationSpecificReportComponent,
    AdSettingsComponent,
    UamReportComponent,
    RoleComponent,
    UsersReportComponent,
    DisabledUsersComponent,
    ActiveUsersComponent,
    MonthlyrawReportComponent,
    PtsReportsComponent,
    EnhancedLocationReportComponent,
    MessagesReportComponent,
    RtlsMessagesReportComponent,
    UamStaffReportComponent,
    UamReportMasterExportComponent,
  ],

  imports: [
    CommonModule,
    SinglePageComponentRoutingModule,
    SharedModule,
    SharedThemeModule,
    DependencyModule,
    MatToolbarModule,
    MatTabsModule,
    NgxMatSelectSearchModule,
  ],
})
export class SinglePageComponentModule {}
