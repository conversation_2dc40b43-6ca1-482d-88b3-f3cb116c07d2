<table class="table" #roleReportDumb id="roleReportDumb" style="display: none">
    <thead>
      <tr>
        <th scope="col">Role</th>
        <th scope="col">Module Availability</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let role of roles$ | async">
        <td>{{ role.role_name }}</td>
        <td> <mat-icon>{{ role.status }}</mat-icon></td>
      </tr>
    </tbody>
  </table>

  <table class="table" #staffReportDumb id="staffReportDumb" style="display: none">
    <thead>
        <tr>
            <th scope="col"><b>Staff Name</b></th>
            <th scope="col"><b>Last Login</b></th>
            <th scope="col"><b>Mobile App(Porter)</b></th>
            <th scope="col"><b>Status</b></th>
        </tr>
    </thead>
    <tbody>
        <ng-container *ngFor="let module of staffList$ | async">
            <tr>
                <td>
                    {{ module?.StaffName }}
                </td>
                <td>
                    {{ module?.LastLogin }}
                </td>
                <td><mat-icon>{{ module.IsPorter }}</mat-icon>
                </td>
                <td>
                    {{ module.Status?"Active":"InActive" }}
                </td>
            </tr>
        </ng-container>
    </tbody>
</table>

<table class="table" style="display: none" #uamReportDumb id="uamReportDumb">
    <tr>
      <td>Modules</td>
      <td>Roles</td>
    </tr>
    <thead>
      <tr>
        <th scope="col" *ngFor="let roles of roles$ | async">
          {{ roles.role_name }}
        </th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let module of roleModuleAllAssign$ | async">
        <tr>
          <td>
            {{ module?.sub_menu }}
          </td>
          <ng-container *ngFor="let roleModule of module?.roles">
            <td>
              <mat-icon class="mat_icon_css">{{
                roleModule ? true : false
              }}</mat-icon>
            </td>
          </ng-container>
        </tr>
      </ng-container>
    </tbody>
  </table>

  <table class="table" #userReportDumb id="userReportDumb" style="display: none">
    <thead>
      <tr>
        <th scope="col">FullName</th>
        <th scope="col">ADID</th>
        <th scope="col" *ngFor="let roles of roles$ | async">
          {{ roles?.role_name }}
        </th>
        <th scope="col">Last Logged in date</th>
        <th scope="col">Last Login Location</th>
        <th scope="col">Created Date</th>
        <th scope="col">Created By</th>
        <th scope="col">Remarks</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let module of userRoleDataMapping$ | async">
        <tr>
          <td>
            {{ module?.FullName }}
          </td>
          <td>
            {{ module?.username }}
          </td>
          <ng-container *ngFor="let roleModule of module?.userRoleAssign">
            <td>
              <mat-icon>{{ roleModule ? true : false }}</mat-icon>
            </td>
          </ng-container>
          <td>
            {{ module.last_login_date | localDateConversion: "full" }}
          </td>
          <td>
            {{ module.last_login_location }}
          </td>
          <td>
            {{ module.created_date | localDateConversion: "full" }}
          </td>
          <td>
            {{ module.created_by }}
          </td>
          <td>
            {{ module.remarks }}
          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>
  