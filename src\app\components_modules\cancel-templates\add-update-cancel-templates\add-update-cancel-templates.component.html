<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
                            <ul class="nav">
                                <li class="nav">
                                    <p *ngIf="!activatedRoute.snapshot.params.id">Add Cancel Message Template</p>
                                    <p *ngIf="activatedRoute.snapshot.params.id">Update Cancel Message Template</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="serviceRequests">
                            <div class="row">
                                <div class="col-12">
                                    <form [formGroup]="messageForm">
                                        <fieldset class="scheduler-border">
                                            <legend></legend>
                                            <div class="row">
                                                <div class="col-12">
                                                    <mat-form-field>
                                                        <mat-label>Message <span class="error-css"><span
                                                                    class="error-css">*</span></span>
                                                        </mat-label>
                                                        <input matInput #desc placeholder="Message" maxlength="300"
                                                            formControlName="cancel_template">
                                                        <mat-hint style="text-align: end;">{{desc.value.length}} / 300
                                                        </mat-hint>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message
                                                                [control]="messageForm.controls.cancel_template"
                                                                [fieldName]="'Message'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-12">
                                                    <mat-form-field>
                                                        <mat-label>Reason <span class="error-css"></span>
                                                        </mat-label>
                                                        <textarea matInput #reason maxlength="300" placeholder="Reason"
                                                            formControlName="reason" rows="1"></textarea>
                                                        <mat-hint style="text-align: end;">{{reason.value.length}} / 300
                                                        </mat-hint>
                                                        <mat-error class="pull-left error-css">
                                                            <app-error-message [control]="messageForm.controls.reason"
                                                                [fieldName]="'Reason'" [fieldType]="'enter'">
                                                            </app-error-message>
                                                        </mat-error>
                                                    </mat-form-field>
                                                </div>
                                                <div class="col-6">
                                                    <br>
                                                    <div class="alignInput">
                                                        <mat-checkbox formControlName="allow_remarks"></mat-checkbox>
                                                        <mat-label>Allow Remarks<span class="error-css"></span>
                                                        </mat-label>
                                                    </div>

                                                </div>
                                            </div>
                                        </fieldset>
                                    </form>
                                    <div class="row from-submit">
                                        <div class="col">
                                            <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveCancelTemplate('add')">Submit</button>
                                            <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                                                class="btn btn-primary pull-right"
                                                (click)="saveCancelTemplate('update')">Update</button>
                                            <button mat-raised-button (click)="messageForm.reset(); getCancelTemplate()"
                                                class="btn btn-white pull-right">Reset</button>
                                        </div>
                                    </div>
                                    <!-- <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                                        <legend class="scheduler-border-log">
                                            Edit Logs
                                        </legend>
                                        <div class="row">
                                            <div class="col-12">
                                                <label class="col-4">Edited By</label>
                                                <label class="col-4">Status</label>
                                                <label class="col-4">Edited On</label>
                                            </div>
                                        </div>
                                        <div class="row" *ngFor="let log of editLogs">
                                            <div class="col-4">
                                                {{log.edited_by}}
                                            </div>
                                            <div class="col-4">
                                                {{log?.approval_status == 0 ? 'Pending' : log?.approval_status == 1 ? 'Approved' : 'Rejected'}}
                                            </div>
                                            <div class="col-4">
                                                {{log.edited_date | localDateConversion: "full"}}
                                            </div>
                                        </div>
                                    </fieldset> -->
                                    <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                                        <legend class="scheduler-border-log">
                                            Audit Logs
                                          </legend>
                                          <div class="mb-8">
                                            <div class="logs-header row">
                                              <div style="max-width: 150px;min-width: 150px;" class="text-center">Message</div>
                                              <div class="col-1 text-center">Status</div>
                                              <div style="max-width: 100px;min-width: 100px;" class="text-center">Approval Status</div>
                                              <div class="col-2 text-center">Request By</div>
                                              <div class="col-2 text-center">Request Date</div>
                                              <div class="col-2 text-center">Approved By</div>
                                              <div style="max-width: 100px;min-width: 100px;" class="text-center">Approved Date</div>
                                            </div>
                                          </div>
                                        <div class="" *ngFor="let log of editLogs; let i = index">
                                            <div class="card card-xl-stretch">
                                                <div class="card-body card-body-log pt-2 row m-2">
                                                    <div style="max-width: 150px;min-width: 150px;" class="text-muted text-center fw-bold">{{log?.name || '--'}}</div>
                                                    <div class="col-1 text-muted text-center fw-bold" style="white-space: nowrap;">{{log?.status == "true" ? 'Active' :
                                                        'In-Active'}}</div>
                                                    <div style="max-width: 100px;min-width: 100px;" class="text-muted text-center fw-bold">{{log?.approvalStatus || '--'}}</div>
                                                    <div class="col-2 text-muted text-center fw-bold">{{log?.created_By || '--'}}
                                                    </div>
                                                    <div class="col-2 text-muted text-center fw-bold">{{log?.created_Date ?
                                                        (log?.formattedCreatedDate) : '--'}}</div>
                                                    <div class="col-2 text-muted text-center fw-bold">{{log?.approved_By || '--'}}
                                                    </div>
                                                    <div style="max-width: 100px;min-width: 100px;" class="text-muted text-center fw-bold">{{log?.approved_Date ?
                                                        (log?.formattedApprovedDate) : '--'}}</div>
                                                </div>
                                            </div>
                                            <span *ngIf="i !== editLogs.length-1" style='font-size:35px;'
                                                class="text-muted">&#8593;</span>
                                        </div>
                                    </fieldset>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>