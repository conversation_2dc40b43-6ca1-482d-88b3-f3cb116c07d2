<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="">
              <p style="float: right">
                <a
                  class="nav-link add-tower"
                  href="javascript:void(0);"
                  [matMenuTriggerFor]="sub_menu_language"
                >
                  Export
                </a>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a
                    style="margin-top: -5px; cursor: pointer; color: #555555"
                    class="nav-link"
                  >
                    <p
                      style="display: inline-block"
                      (click)="exportTable('xsls')"
                    >
                      xsls
                    </p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li>
                  <p>Station Workload by Location</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12">
                  <app-searchfilter></app-searchfilter>
                </div>
                <div class="col-md-12 spWorkLoad">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table
                      id="stationpoolbylocation"
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                    >
                      <caption></caption>
                      <ng-container matColumnDef="item">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Stations
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.item }}</td>
                      </ng-container>

                      <ng-container matColumnDef="YTDPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.YTDPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="YTDNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.YTDNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="YTDTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.YTDTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="JanPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.JanPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="JanNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.JanNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="JanTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.JanTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="FebPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.FebPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="FebNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.FebNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="FebTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.FebTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="MarPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.MarPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="MarNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.MarNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="MarTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.MarTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="AprPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.AprPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="AprNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.AprNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="AprTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.AprTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="MayPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.MayPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="MayNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.MayNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="MayTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.MayTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="JunPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.JunPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="JunNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.JunNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="JunTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.JunTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="JulPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.JulPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="JulNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.JulNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="JulTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.JulTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="AugPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.AugPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="AugNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.AugNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="AugTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.AugTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="SepPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.SepPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="SepNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.SepNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="SepTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.SepTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="OctPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.OctPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="OctNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.OctNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="OctTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.OctTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="NovPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.NovPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="NovNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.NovNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="NovTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.NovTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="DecPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          PM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.DecPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="DecNPM">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          NPM
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.DecNPM }}</td>
                      </ng-container>

                      <ng-container matColumnDef="DecTotal">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                        >
                          Total
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.DecTotal }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="header-row-zero-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="1"
                        ></th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-first-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          YTD AVG-{{
                            dates && dates.year ? dates.year : currYear
                          }}
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-second-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Jan-{{ dates && dates.year ? dates.year : currYear }}
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-third-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Feb-{{ dates && dates.year ? dates.year : currYear }}
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-fourth-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Mar-{{ dates && dates.year ? dates.year : currYear }}
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-fifth-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Apr-{{ dates && dates.year ? dates.year : currYear }}
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-sixth-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          May-{{ dates && dates.year ? dates.year : currYear }}
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-seventh-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Jun-{{ dates && dates.year ? dates.year : currYear }}
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-eighth-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Jul-{{ dates && dates.year ? dates.year : currYear }}
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-ninth-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Aug-{{ dates && dates.year ? dates.year : currYear }}
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-tenth-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Sep-{{ dates && dates.year ? dates.year : currYear }}
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-eleventh-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Oct-{{ dates && dates.year ? dates.year : currYear }}
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-twelveth-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Nov-{{ dates && dates.year ? dates.year : currYear }}
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="header-row-thirtheenth-group">
                        <th
                          id=""
                          mat-header-cell
                          *matHeaderCellDef
                          [attr.colspan]="3"
                        >
                          Dec-{{ dates && dates.year ? dates.year : currYear }}
                        </th>
                      </ng-container>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="[
                          'header-row-zero-group',
                          'header-row-first-group',
                          'header-row-second-group',
                          'header-row-third-group',
                          'header-row-fourth-group',
                          'header-row-fifth-group',
                          'header-row-sixth-group',
                          'header-row-seventh-group',
                          'header-row-eighth-group',
                          'header-row-ninth-group',
                          'header-row-tenth-group',
                          'header-row-eleventh-group',
                          'header-row-twelveth-group',
                          'header-row-thirtheenth-group'
                        ]"
                      ></tr>

                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                      ></tr>
                    </table>
                    <div
                      *ngIf="dataSource && dataSource.filteredData.length === 0"
                    >
                      No records to display.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <mat-paginator
          [pageSizeOptions]="[10, 25, 50, 100]"
          pageSize="50"
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
