import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from "@angular/core";
import { Mat<PERSON>idenav, MatTabChangeEvent } from "@angular/material";
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { RoutingService } from "src/app/Apiservices/routingService/routing.service";
import { ReportMenuStatic } from "src/app/config/report-menu.config";

@Component({
  selector: "app-kpi-report-home",
  templateUrl: "./kpi-report-home.component.html",
  styleUrls: ["./kpi-report-home.component.scss"],
})
export class KpiReportHomeComponent implements OnInit, OnDestroy {
  @ViewChild("sidenav", { static: true }) sidenav: MatSidenav;
  sampleSidenav$: any;
  navLink: any = [];
  activeParent: any = null;
  activeSubmenu: any = null;
  routerSub: any;
  reportMenuSub: any;
  constructor(
    private _facilityConfig: FacilityConfigService,
    public routingService: RoutingService,
    private activatedRoute: ActivatedRoute,
    private router: Router
  ) { }

  ngOnInit() {
    this.getReportMenu();
  }

  getReportMenu() {
    this.sampleSidenav$ = ReportMenuStatic;
    this.reportMenuSub = ReportMenuStatic.subscribe((data: any) => {
      this.navLink = data.filter(
        (menu) =>
          menu.menu == this.activatedRoute.snapshot.firstChild.data["name"]
      );
      this.navLink = this.navLink !== null ? this.navLink[0].submenu_list : [];

      if (!this.activeParent && this.navLink.length > 0) {
        this.activeParent = this.navLink[0];
        if (this.activeParent.submenu && this.activeParent.submenu.length > 0) {
          this.activeSubmenu = this.activeParent.submenu[0];
          const url = this.routingService.urlAssignToMenu(this.activeSubmenu.name);
          this.router.navigate([url]);
        }
      }
    });
  }

  isParentActive(link: any): boolean {
    return this.activeParent === link;
  }

  setActiveSubmenu(parent: any, submenu: any) {
    this.activeParent = parent;
    this.activeSubmenu = submenu;
  }

  ngOnDestroy() {
    if (this.routerSub) {
      this.routerSub.unsubscribe();
    }
    if (this.reportMenuSub) {
      this.reportMenuSub.unsubscribe();
    }
  }
}
