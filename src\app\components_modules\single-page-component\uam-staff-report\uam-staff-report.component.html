<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="card card-nav-tabs">
                <div class="card-header card-header-primary card__header__grey">
                    <div class="nav-tabs-navigation">
                        <div class="">
                            <p style="float: right">
                                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                                    Export
                                </button>
                            </p>
                            <mat-menu #sub_menu_language="matMenu">
                                <br />
                                <span style="height: 25px" class="nav-item">
                                    <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('xsls')">
                                            xsls
                                        </p>
                                    </a>
                                    <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                                        <p style="display: inline-block" (click)="exportTable('pdf')">
                                            PDF
                                        </p>
                                    </a>
                                </span>
                            </mat-menu>
                            <ul class="nav">
                                <li>
                                    <p>Manage Staff</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content text-center">
                        <div class="tab-pane active" id="viewStatus">
                            <div class="row">
                                <div class="col-md-12 filter-margin"></div>
                                <div class="col-md-12">
                                    <div class="mat-elevation-z8" style="overflow-x: auto">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th scope="col">Staff Name</th>
                                                    <th scope="col">Last Login</th>
                                                    <th scope="col">Mobile App(Porter)</th>
                                                    <th scope="col">Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <ng-container *ngFor="let module of staffList$ | async">
                                                    <tr>
                                                        <td>
                                                            {{ module?.StaffName }}
                                                        </td>
                                                        <td>
                                                            {{ module?.LastLogin }}
                                                        </td>
                                                        <td> <mat-icon>{{
                                                                module.IsPorter ? "done" : "clear"
                                                                }}</mat-icon>
                                                        </td>
                                                        <td>
                                                            {{ module.Status?"Active":"InActive" }}
                                                        </td>
                                                    </tr>
                                                </ng-container>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<app-table-for-pdf [heads]="[
      'StaffName',
      'LastLogin',
      'MobileAppPorter',
      'Status']" [title]="'Staff Report'" [datas]="pdfData">
</app-table-for-pdf>

<!-- this is table of user report only to export excel -->
<table class="table" id="staffReportDumb" style="display: none">
    <thead>
        <tr>
            <th scope="col"><b>Staff Name</b></th>
            <th scope="col"><b>Last Login</b></th>
            <th scope="col"><b>Mobile App(Porter)</b></th>
            <th scope="col"><b>Status</b></th>
        </tr>
    </thead>
    <tbody>
        <ng-container *ngFor="let module of staffList$ | async">
            <tr>
                <td>
                    {{ module?.StaffName }}
                </td>
                <td>
                    {{ module?.LastLogin }}
                </td>
                <td><mat-icon>{{ module.IsPorter }}</mat-icon>
                </td>
                <td>
                    {{ module.Status?"Active":"InActive" }}
                </td>
            </tr>
        </ng-container>
    </tbody>
</table>