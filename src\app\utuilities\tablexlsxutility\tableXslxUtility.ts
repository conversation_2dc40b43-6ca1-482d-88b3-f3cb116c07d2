import { EnhancedReport } from "src/app/models/enhancedReport";
import * as XLSX from "xlsx";
// import { formatDate } from '@angular/common';
import * as moment from 'moment';
import { saveAs } from 'file-saver';

export class TableUtil {
  static exportToExcel(tableId: string, name?: string) {
    const timeSpan = new Date().toUTCString();
    const prefix = name || "ExportResult";
    const fileName = `${prefix}-${timeSpan}`;
    const targetTableElm = document.getElementById(tableId);
    // tslint:disable-next-line: no-angle-bracket-type-assertion
    const wb = XLSX.utils.table_to_book(targetTableElm, <XLSX.Table2SheetOpts>{
      sheet: prefix,
      raw: true,
    });

    Object.keys(wb.Sheets[prefix]).forEach((res) => {
      const v = "v";

      if (
        wb.Sheets[prefix][res][v] === "Edit" ||
        wb.Sheets[prefix][res][v] === "edit" ||
        wb.Sheets[prefix][res][v] === "editdelete" ||
        wb.Sheets[prefix][res][v] === "Delete" ||
        wb.Sheets[prefix][res][v] === "delete" ||
        wb.Sheets[prefix][res][v] === "more_vert" ||
        wb.Sheets[prefix][res][v] === "Actions"
      ) {
        delete wb.Sheets[prefix][res][v];
      }
    });

    XLSX.writeFile(wb, `${fileName}.xlsx`);
    return false;
  }

  static exportArrayToExcel(arr: any[], showAck: any, name?: string, type?: any) {

    let expobj: any[] = [];
    let tmpobj: any;
    let task_time: any;
    let tstdate: any;
    for (var i = 0; i < arr.length; i++) {

      // tstdate = formatDate('2019-04-13T00:00:00', 'dd/MM/yyyy hh:MM', 'en-US');
      let getDate = moment('2019-04-13T00:00:00').format('MM-DD-YYYY hh:mm');
      if (arr[i].request_type == "Normal" || arr[i].request_type == "Urgent") {
        task_time = arr[i].created_date ? arr[i].created_date : "--";
      } else {
        task_time = arr[i].due_time ? arr[i].due_time : "--";
      }
      if (arr[i].within_kpi == "1") {
        arr[i].within_kpi = "Yes";
      } else {
        arr[i].within_kpi = "No";
      }
      if (arr[i].modified_date != "") {
        arr[i].modified_date = arr[i].modified_date ? arr[i].modified_date : "--";
      }
      if (arr[i].smart_assigned == "1") {
        arr[i].smart_assigned = "Yes";
      } else {
        arr[i].smart_assigned = "No";
      }
      tmpobj = {
        "Job No": arr[i].order_no,
        "Request Type": arr[i].request_type,
        "Order From": arr[i].order_from ? arr[i].order_from.toString().replace("\r\n", "") : "--",
        // toString().replace("\r\n",""),
        "Task Time": task_time || '',
        "Assigned Time": arr[i].assign_time || '',
        "Start Time": arr[i].start_time || '',
        "Completion Time": arr[i].completion_time || '',
        "Cancel Time": arr[i].cancel_time || '',
        "Requestor": arr[i].requestor,
        "Patient Name": arr[i].patient_name,
        "NRIC": arr[i].nric,
        "From": arr[i].from_location ? arr[i].from_location.toString().replace("\r\n", "") : "--",
        "From Room": arr[i].from_room,
        "From Bed": arr[i].from_bed,
        "To": arr[i].to_location ? arr[i].to_location.toString().replace("\r\n", "") : "--",
        "To Room": arr[i].to_room,
        "To Bed": arr[i].to_bed,
        "Return": arr[i].return_location,
        "Return Bed": arr[i].return_bed,
        "Remarks": arr[i].remarks,
        "Job Status": arr[i].job_status,
        "Main Category": arr[i].main_category,
        "Sub Category": arr[i].sub_category,
        "Transport Mode": arr[i].transport_mode,
        "Std KPI": arr[i].std_kpi,
        "Req-Resp(Mins)": arr[i].req_resp,
        "Resp-Comp(Mins)": arr[i].resp_comp,
        "Within KPI": arr[i].within_kpi,
        "Smart Assigned": arr[i].smart_assigned,
        "Isolation Precaution": arr[i].isolation_precaution,
        "Resource 1": arr[i].resource1,
        "Resource 2": arr[i].resource2,
        "Creation Time": arr[i].created_date,
        "Created By": arr[i].created_by,
        "Modified By": arr[i].modified_by,
        "Modified Date": arr[i].modified_date,
        "Assigned By": arr[i].assigned_by,
        "Acknowledged Time": arr[i].porterack_time,
        "Patient IC scan": arr[i].scan_patient === true ? "Yes" : "No",
        "Patient IC Scanned Time": arr[i].patient_scan_time,
        "Delay Reason": arr[i].delay_reason,
        "Cancel Reason": arr[i].cancel_reason,
        "Cancel Remarks": arr[i].cancel_remarks,
        "Cancelled By": arr[i].cancelled_by,

        // "Ack":arr[i].ack
      };
      if (showAck === true) {
        tmpobj["ACK"] = arr[i].ack ? arr[i].ack : "--";
      }

      expobj.push(tmpobj);
    }

    const timeSpan = new Date().toUTCString();
    const prefix = name || "ExportResult";
    const fileName = `${prefix}-${timeSpan}`;
    var wb = XLSX.utils.book_new();
    var ws = XLSX.utils.json_to_sheet(expobj);
    XLSX.utils.book_append_sheet(wb, ws, 'sheet1');
    if (type == 'csv') {
      XLSX.writeFile(wb, `${fileName}.csv`);
    } else {
      XLSX.writeFile(wb, `${fileName}.xlsx`);
    }
  }

  static exportArrayToExcelDynamically(arr: any[], name?: string) {
    const timeSpan = new Date().toUTCString();
    const prefix = name || "ExportResult";
    const fileName = `${prefix}-${timeSpan}`;
    var wb = XLSX.utils.book_new();
    var ws = XLSX.utils.json_to_sheet(arr);
    XLSX.utils.book_append_sheet(wb, ws, 'sheet1');
    XLSX.writeFile(wb, `${fileName}.xlsx`);
  }

  static exportArrayToCsvDynamically(arr: any[], name?: string) {
    const timeSpan = new Date().toUTCString();
    const prefix = name || "ExportResult";
    const fileName = `${prefix}-${timeSpan}`;
    var wb = XLSX.utils.book_new();
    var ws = XLSX.utils.json_to_sheet(arr);
    XLSX.utils.book_append_sheet(wb, ws, 'sheet1');
    XLSX.writeFile(wb, `${fileName}.csv`);
  }

  static exportTablesToExcel(tables, tableNames): void {
    debugger;
    const timeSpan = new Date().toUTCString();
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    tables.forEach((tableRef, index) => {
      if (tableRef) {
        const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(tableRef.nativeElement);
        for (const cell in ws) {
          if (ws.hasOwnProperty(cell) && cell[0] !== '!') { // Ignore special keys like !ref
            // Check if the cell's value is boolean true or string 'true'
            if (ws[cell].v === true || ws[cell].v === 'true') {
              ws[cell].v = '✓'; // Replace with a tick mark
              ws[cell].t = 's'; // Ensure the type is set to string
            }
            else if (ws[cell].v === false || ws[cell].v === 'false') {
              ws[cell].v = ''; 
              ws[cell].t = 's'; 
            }
          }
        }
        XLSX.utils.book_append_sheet(wb, ws, tableNames[index]);
      }
    });

    const excelBuffer: any = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    const file = new Blob([excelBuffer], { type: 'application/octet-stream' });
    saveAs(file, `UAM_Report-${timeSpan}.xlsx`);
  }
}
