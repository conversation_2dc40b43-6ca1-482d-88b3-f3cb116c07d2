import { Injectable } from "@angular/core";
import { HttpService } from "../httpService/http.service";
import { Observable, BehaviorSubject } from "rxjs";
import { environment } from "src/environments/environment";
import { EnhancedReport } from "src/app/models/enhancedReport";

@Injectable({
  providedIn: "root",
})
export class ReportsService {
  searchDates: BehaviorSubject<any> = new BehaviorSubject<any>({});
  constructor(private readonly httpService: HttpService) {}

  getPatientLog(from?, to?): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/patientlogs?from=${from ? from : ""}&to=${
        to ? to : ""
      }`
    );
  }
  getFeedBackReport(body): Observable<any> {
    return this.httpService.post(
      `${environment.base_url}api/feedbackreports`,
      body
    );
  }
  enhancedBusRoute(body) {
    return this.httpService.post(
      `${environment.base_url}/api/enhancedreports/busroutereports`,
      body
    );
  }
  getPorterReport(body): Observable<any> {
    return this.httpService.post(
      `${environment.base_url}api/enhancedreports/portersummary`,
      body
    );
  }
  getEnhancedReportSearch(body): Observable<any> {
    return this.httpService.post(
      `${environment.base_url}api/enhancedreports/search`,
      body
    );
  }

  getPTSReportSearch(body): Observable<any> {
    return this.httpService.post(
      `${environment.base_url}api/enhancedreports/pts`,
      body
    );
  }
  
  getSpecificPorterReports(type, body): Observable<any> {
    return this.httpService.post(
      `${environment.base_url}api/reports/${type ? type : ""}`,
      body
    );
  }

  getSummaryLocationReport(fromDate, toDate): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/summaryreport/location?fromdate=${fromDate}&todate=${toDate}`
    );
  }
  getSummaryHourlyReport({ from_date, to_date, day }): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/summaryreport/hourly?fromdate=${from_date}&todate=${to_date}&daytype=${day}`
    );
  }
  getEquipmentReport(fromDate, toDate): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/summaryreport/equipment?fromdate=${fromDate}&todate=${toDate}`
    );
  }
  getTaskTypeReport(fromDate, toDate): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/summaryreport/transportationtype?fromdate=${fromDate}&todate=${toDate}`
    );
  }

  getKpiReport(fromDate, toDate): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/summaryreport/kpi?fromdate=${fromDate}&todate=${toDate}`
    );
  }
  getOngoingBusRoutes(data?): Observable<any> {
    return this.httpService.post(`${environment.base_url}api/busroutes/ongoing`, data);
  }

  assignreassignBusRoute(data, id, url) {
    return this.httpService.put(
      `${environment.base_url}api/busroutes/ongoing/${id}/${url}`,
      data
    );
  }

  assignReassignBusRouteLocation(data, id, url, location_id) {
    return this.httpService.put(
      `${environment.base_url}api/busroutes/ongoing/${id}/${url}`,
      data
    );
  }

  getkpiReports(type, year, month): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/kpi/${type}/${year}/${month}`
    );
  }

  getkpiReportsCpJobType(type, year, month, typecp): Observable<any> {
    return this.httpService.get(
      `${environment.base_url}api/kpi/${type}/${year}/${month}/${typecp}`
    );
  }

  searchStationReport(data): Observable<any> {
    const body = {
      'locationId': data.locationsList,
      'staffId': data.staff,
      'fromDate': data.from,
      'toDate': data.to,
      'pageNo': data.pageNo || 1,
      'pageSize': data.pageSize || 50
    }
    // tslint:disable-next-line: max-line-length
    return this.httpService.post(
      `${environment.base_url}api/reports/stationporter`, body
    );
  }

  getMonthlyReports(data) {
    const URL = `${environment.base_url}api/monthlyreports/download?month=${data.month}&year=${data.year}`;
    return window.open(URL, "_blank");
    // return this.httpService.get(`${environment.base_url}api/monthlyreports/download?month=${data.month}&year=${data.year}`)
  }

  getMessageReports(data?: any) {
    const URL = `${environment.base_url}api/messageReports`;
    return this.httpService.post(URL, data);
  }

  getRTLSMessageReports(data?: any) {
    const URL = `${environment.base_url}api/messageReports/rtls`;
    return this.httpService.post(URL, data);
  }
}
