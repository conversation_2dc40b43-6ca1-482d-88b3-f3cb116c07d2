import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { map, tap } from 'rxjs/operators';
import { UamReportsService } from '../uam-report/uam-reports.service';

@Component({
  selector: 'app-uam-report-master-export',
  templateUrl: './uam-report-master-export.component.html',
  styleUrls: ['./uam-report-master-export.component.scss']
})
export class UamReportMasterExportComponent implements OnInit {
  @ViewChild('userReportDumb', { static: false }) userReportDumb!: ElementRef;
  @ViewChild('uamReportDumb', { static: false }) uamReportDumb!: ElementRef;
  @ViewChild('staffReportDumb', { static: false }) staffReportDumb!: ElementRef;
  @ViewChild('roleReportDumb', { static: false }) roleReportDumb!: ElementRef;

  roleModule$: any;
  roleModuleAllAssign$: any;
  allRoles: any;
  userList$: any;
  userRoleDataMapping$: any;
  roles$: any;
  allRoles$: any;
  allRoleForExcel$: any;
  staffList$: any;

  constructor(private uamReportService: UamReportsService) {

  }

  ngOnInit() {
    this.getUAMRolesModule();

    this.roles$ = this.uamReportService.getUAMRoles();
    this.allRoles$ = this.roles$
      .pipe(
        tap((data: any) => {
          data.map((roles) => (this.allRolesDictionary[roles.role_name] = ""));
        })
      )
      .subscribe((data) => this.getUserList());
    this.allRoleForExcel$ = this.roles$.pipe(
      map((allRoleExcel: any) => allRoleExcel.map((data) => data.role_name))
    );
    this.staffList$ = this.uamReportService.getUAMStaff();
  }

  getUserList() {
    this.userList$ = this.uamReportService.getUAMUsers();
    this.userRoleDataMapping$ = this.uamReportService.getUserRoleDataMapping(
      this.userList$,
      this.allRolesDictionary
    );
  }
  allRolesDictionary(userList$: any, allRolesDictionary: any): any {
    throw new Error('Method not implemented.');
  }

  getUAMRolesModule() {
    this.roleModule$ = this.uamReportService.getUAMRolesModule();
    this.roleModuleAllAssign$ = this.roleModule$.pipe(
      map((data: any) => {
        return data.map((module_name) => {
          const allRolesDefault = { ...this.allRoles };
          const roleAssign = [];
          module_name.roles.forEach((dataValue) => {
            allRolesDefault[dataValue] = dataValue;
          });
          const roleValue = Object.keys(allRolesDefault).map(
            (key) => allRolesDefault[key]
          );
          return { ...module_name, roles: roleValue };
        });
      })
    );
  }
}
