import { Component, OnInit } from "@angular/core";
import { Location } from "@angular/common";
import { ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { MasterDataService } from "src/app/Apiservices/masterData/master-data.service";
import Swal from 'sweetalert2';

@Component({
  selector: "app-add-update-subjobcategory",
  templateUrl: "./add-update-subjobcategory.component.html",
  styleUrls: ["./add-update-subjobcategory.component.scss"],
})
export class AddUpdateSubjobcategoryComponent implements OnInit {
  subjobcategoryForm: FormGroup;
  towers: {};
  roles: any = [];
  modesOfTransport: any = [];
  masterService: any = [];
  standardTime: any = [];
  subjobPriority: any = [];
  skillLevel: any = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100];
  porters: any = [1, 2];
  timePresent = false;
  validationMessage = { displayIn: "" };
  editLogs: any = [];

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService,
    private readonly userService: UserService,
    private readonly masterDataSErvice: MasterDataService
  ) {
    this.subjobcategoryForm = this.fb.group({
      mj_category_id: ["", Validators.required],
      sj_category_name: ["", Validators.required],
      sj_short_discr: ["", Validators.required],
      std_time_code: ["", Validators.required],
      priority: ["", Validators.required],
      skill_level: ["", Validators.required],
      required_sex: ["others", Validators.required],
      display_in: this.fb.group({
        normal: null,
        advance: null,
        emergency: null,
      }),
      no_of_porters: ["1"],
      external_move: [false],
      status: ["true", Validators.required],
      patient_info: ["", Validators.required],
      start_time: [{ value: "", disabled: true }],
      end_time: [{ value: "", disabled: true }],
      total_time: [{ value: "", disabled: true }],
      round_trip: ["No", Validators.required],
      urgent: [false],
      isolation_precaution: [false],
      role_id_list: [[]],
      equipment_list: ["", Validators.required],
      ack_req: [false],
      patient_move: [false],
      SAStack: [false],
      SAGroup: [false],
      SAAllowed: [false],
      SAStacktothis: [false],
      display_in_station: [false],
      reason: [''],
      approval_status: [''],
      skip_patientic: [false],
      skip_ack: [false],
      SAAllowedJobs: this.fb.group({
        Normal: null,
        Advance: null,
        Emergency: null,
      }),
    });
  }
  ngOnInit() {
    //this.getRoles();
    this.getPriority();
    this.getSubjobById();
    this.getModesOfTransport();
  }

  getModesOfTransport() {
    this.masterDataSErvice.getModeOfTransports().subscribe((res) => {
      res =
        res &&
        res.length &&
        res.filter((val) => {
          if (val.status) {
            return val;
          }
        });
      this.modesOfTransport = res || [];
    });
  }

  getSubjobById() {
    if (this.activatedRoute.snapshot.params.id) {
      this.masterDataSErvice
        .getSubjobById(this.activatedRoute.snapshot.params.id)
        .subscribe((res) => {
          this.editLogs = res.edit_logs || [];
          this.editLogs.sort(function (a: any, b: any) {
            let B: any = new Date(b.created_Date);
            let A: any = new Date(a.created_Date);
            return B - A;
          });
          this.editLogs.map(log => {
            log['formattedCreatedDate'] = log && log.created_Date ? log.created_Date.split('/').join("-") : null
            log['formattedApprovedDate'] = log && log.approved_Date ? log.approved_Date.split('/').join("-") : null
            log['approvalStatus'] = log.approval_Status == '0' ? 'Pending' : log.approval_Status == '1' ? 'Approved' : log.approval_Status == '2' ? 'Rejected' : '--'
          })
          res.status = res.status ? "true" : "false";
          res.round_trip = res.round_trip === true ? "Yes" : "No";
          res.patient_info = res.patient_info === true ? "Yes" : "No";
          res.visible = {
            isolation: false,
            urgent: false,
          };
          this.getmainJobCategory(res.mj_category_id);
          const x = {
            normal:
              res.display_in && res.display_in.includes("N") ? true : false,
            advance:
              res.display_in && res.display_in.includes("A") ? true : false,
            emergency:
              res.display_in && res.display_in.includes("E") ? true : false,
          };
          res.display_in = x;
          const y = {
            Normal:
              res.SAAllowedJobs && res.SAAllowedJobs.includes("Normal") ? true : false,
              Advance:
              res.SAAllowedJobs && res.SAAllowedJobs.includes("Advance") ? true : false,
              Emergency:
              res.SAAllowedJobs && res.SAAllowedJobs.includes("Emergency") ? true : false,
          };
          res.SAAllowedJobs = y;
          this.subjobcategoryForm.patchValue(res);
          this.getStandardT(res.std_time_code);
        });
    } else {
      this.getStandardT();
      this.getmainJobCategory();
    }
  }
  getRoles() {
    this.userService.getRoles().subscribe((res) => {
      res =
        (res &&
          res.length &&
          res.filter((val) => {
            if (val.status) {
              return val;
            }
          })) ||
        [];
      this.roles = res;
    });
  }
  getmainJobCategory(patchvalue?) {
    this.masterDataSErvice.getmainJobCategory().subscribe((res) => {
      res =
        (res &&
          res.length &&
          res.filter((vals) => {
            if (vals.status) {
              return vals;
            }
          })) ||
        [];
      this.masterService = res;
      if (patchvalue) {
        const isActive = this.masterService.findIndex(
          (id) => id.mj_category_id === patchvalue
        );
        if (isActive === -1) {
          this.subjobcategoryForm.get("mj_category_id").setValue("");
        }
      }
    });
  }
  getStandardT(id?) {
    this.facilityConfig.getStandardTime().subscribe((res) => {
      this.standardTime = res;
      if (id) {
        this.timeSelected(id);
      }
    });
  }
  getPriority() {
    this.facilityConfig.getPriority().subscribe((res) => {
      this.subjobPriority = res;
    });
  }
  timeSelected(time) {
    const existingTime = this.standardTime.find((code) => code.std_id === time);
    if (existingTime) {
      this.subjobcategoryForm
        .get("start_time")
        .setValue(existingTime.start_time);
      this.subjobcategoryForm.get("end_time").setValue(existingTime.end_time);
      this.subjobcategoryForm
        .get("total_time")
        .setValue(existingTime.total_time);
    }
  }
  mapDisplayIn() {
    const data = this.subjobcategoryForm.get("display_in").value;
    let displayData = "";
    Object.keys(data).forEach((key) => {
      if (data[key]) {
        displayData
          ? (displayData = displayData + key[0])
          : (displayData = key[0]);
      }
    });
    this.subjobcategoryForm.value.display_in = displayData.toUpperCase();
  }
  mapSmartAssign() {
    const data = this.subjobcategoryForm.get("SAAllowedJobs").value;
    let smartassignData = "";
    Object.keys(data).forEach((key) => {
      if (data[key]) {
        smartassignData
          ? (smartassignData = smartassignData + key + ',')
          : (smartassignData = key+ ',');
      }
    });
    this.subjobcategoryForm.value.SAAllowedJobs = smartassignData;
  }
  saveSubjobCategory(action) {
    this.subjobcategoryForm.get('required_sex').setValue('others');
    let subJobId: any;
    const valuesSubCategory = Object.keys(
      this.subjobcategoryForm.value.display_in
    ).map((e) => {
      return this.subjobcategoryForm.value.display_in[e];
    });
    if (this.subjobcategoryForm.valid && valuesSubCategory.length > 0) {
      const data = this.subjobcategoryForm.value;
      data.visible = JSON.stringify(data.visible);
      data.round_trip = data.round_trip === "Yes" ? true : false;
      data.patient_info = data.patient_info === "Yes" ? true : false;
      data.status = data.status === "true" ? true : false;
      this.mapDisplayIn();
      this.mapSmartAssign();
      if (action === "update") {
        subJobId = Number(this.activatedRoute.snapshot.params.id);
        if (data.approval_status && data.approval_status == 'Pending') {
          Swal.fire({
            title: 'This request is already under process',
            text: `Are you sure, You want to update?`,
            icon: 'warning',
            showCancelButton: true,
            cancelButtonText: 'No',
            confirmButtonText: 'Yes'
          }).then((result) => {
            if (result.value) {
              this.updateSubJobCategory(data, action, subJobId);
            } else if (result.dismiss === Swal.DismissReason.cancel) {
              return;
            }
          });
        } else {
          this.updateSubJobCategory(data, action, subJobId);
        }
      } else {
        this.updateSubJobCategory(data, action, subJobId);
      }
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.validationMessage.displayIn = valuesSubCategory.find(
        (data) => data != null
      )
        ? ""
        : "Please select any one display";
      this.subjobcategoryForm.markAllAsTouched();
    }
  }

  updateSubJobCategory(data, action, subJobId) {
    this.masterDataSErvice
      .addUpadteSubJobCategory(
        this.subjobcategoryForm.value,
        action === "save"
          ? "api/sjcategories/add"
          : `api/sjcategories/edit/${subJobId}`,
        action
      )
      .subscribe(
        (res) => {
          this.location.back();
          this.toastr.success(
            `Successfully ${action === "save" ? "added" : "updated"
            } subjob Category`,
            "Success"
          );
        },
        (err) => { }
      );
  }

}
