.mat-tab-nav-bar {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);

  .mat-tab-link {
    opacity: 1;
    color: rgba(0, 0, 0, 0.87);
    transition: all 0.3s ease;
    cursor: pointer;
    
    &[ng-reflect-active="true"] {
      color: #1b2581;
      opacity: 1;
      font-weight: 500;
      border-bottom: 2px solid #1b2581;
    }
  }
}

::ng-deep .mat-menu-panel {
  .mat-menu-item {
    &:hover {
      color: #1b2581;
      background-color: rgba(27, 37, 129, 0.04);
    }
    
    &.active-submenu {
      color: #1b2581;
      font-weight: 500;
      background-color: #e7e7e7;
    }
  }
}
