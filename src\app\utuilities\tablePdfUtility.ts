import * as jspdf from 'jspdf';
import html2canvas from 'html2canvas';
import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root'
})
export class TableUtilPDf {

    static async exportToPdf(tableId: string, name?: string, pageType?) {
        const timeSpan = new Date().toUTCString();
        const prefix = name || 'ExportResult';
        const fileName = `${prefix}-${timeSpan}`;
        const targetTableElm = document.getElementById('pdf-download');
        const genrated = await html2canvas(targetTableElm).then(canvas => {
            const imgWidth = pageType ? pageType : 210;
            const pageHeight = 320;
            const imgHeight = canvas.height * imgWidth / canvas.width;
            let heightLeft = imgHeight;
            const contentDataURL = canvas.toDataURL('image/png');
            const pdf = new jspdf(`${pageType ? 'l' : 'p'}`, 'mm', 'true', true);
            let position = 0;
            if (contentDataURL === 'data:,') {
                return 'failed to genrate';
            }
            pdf.addImage(contentDataURL, 'PNG', 0, position, imgWidth, imgHeight, '', 'FAST');
            heightLeft -= pageHeight;
            while (heightLeft >= 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(contentDataURL, 'PNG', 0, position, imgWidth, imgHeight, '', 'FAST');
                heightLeft -= pageHeight;
            }
            pdf.save(`${fileName}.pdf`);
            return 'generated';
        }).catch(() => {
            return 'failed';
        });
        if (genrated) {
            return genrated;
        }
    }
}
