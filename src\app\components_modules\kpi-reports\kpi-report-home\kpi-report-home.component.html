<!-- <mat-sidenav-container
  class="example-container h-100"
  (backdropClick)="close('backdrop')"
>
  <mat-sidenav
    #sidenav
    (keydown.escape)="close('escape')"
    disableClose
    opened="true"
    mode="side"
    class="sidenav__css"
  >
    <div class="container-fluid">
      <div class="row mt-3">
        <div class="col-10">
          <p class="sidenav__report__header">Reports</p>
        </div>
        <div class="col-1"></div>
        <div class="col-12">
          <mat-list role="list">
            <ng-container *ngFor="let menu of sampleSidenav$ | async">
              <mat-list-item
                role="listitem"
                class="menu__option"
                [ngClass]="
                  menu.submenu_list?.length > 0 ? '' : 'menu__option_cursor'
                "
                [routerLinkActive]="
                  menu.submenu_list?.length > 0 ? '' : 'active'
                "
                [routerLink]="
                  menu.submenu_list?.length > 0
                    ? null
                    : routingService.urlAssignToMenu(menu.menu)
                "
              >
                <span class="">{{ menu.menu }}</span>
              </mat-list-item>
              <mat-list class="ml-4">
                <div *ngFor="let subItem of menu.submenu_list">
                  <mat-list-item
                    class="menu__option"
                    [ngClass]="
                      menu.submenu?.length > 0 ? '' : 'menu__option_cursor'
                    "
                    [routerLinkActive]="
                      subItem.submenu?.length > 0 ? '' : 'active'
                    "
                    [routerLink]="
                      routingService.urlAssignToMenu(
                        subItem.name == 'Roles' ? 'Roles-reports' : subItem.name
                      )
                    "
                  >
                    <span>
                      {{ subItem.name }}
                    </span>
                  </mat-list-item>

                  <mat-list class="ml-4">
                    <div *ngFor="let subItemLevelThird of subItem.submenu">
                      <mat-list-item
                        class="menu__option menu__option_cursor"
                        routerLinkActive="active"
                        [routerLink]="
                          routingService.urlAssignToMenu(subItemLevelThird.name)
                        "
                      >
                        <span>{{ subItemLevelThird.name }}</span>
                      </mat-list-item>
                    </div>
                  </mat-list>
                </div>
              </mat-list>
            </ng-container>
          </mat-list>
        </div>
      </div>
    </div>
  </mat-sidenav>

  <mat-sidenav-content>
    <button mat-button (click)="close('toggle button')" class="menu__arrow">
      <mat-icon class="arrow"
        >{{ sidenav.opened ? "keyboard_arrow_left" : "keyboard_arrow_right" }}
      </mat-icon>
    </button>
    <div class="">
      <router-outlet></router-outlet>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container> -->

<nav mat-tab-nav-bar class="mb-4">
  <a
    mat-tab-link
    *ngFor="let link of navLink"
    [matMenuTriggerFor]="menu"
    [active]="isParentActive(link)"
    (click)="$event.preventDefault()"
  >
    <mat-menu #menu="matMenu">      
      <button
        mat-menu-item
        *ngFor="let thirdMenu of link.submenu"
        [routerLink]="routingService.urlAssignToMenu(thirdMenu.name)"
        [class.active-submenu]="activeSubmenu === thirdMenu"
        (click)="setActiveSubmenu(link, thirdMenu)"
      >
        {{ thirdMenu.name }}
      </button>
    </mat-menu>
    {{ link.name }}
  </a>
</nav>
<router-outlet></router-outlet>
