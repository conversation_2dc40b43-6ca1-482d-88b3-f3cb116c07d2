import { Component, OnInit } from "@angular/core";
import { map, tap } from "rxjs/operators";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { LocalDateConversionPipe } from "src/app/shared/pipes/local-date-conversion.pipe";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { UamReportsService } from "../uam-report/uam-reports.service";

@Component({
  selector: 'app-uam-staff-report',
  templateUrl: './uam-staff-report.component.html',
  styleUrls: ['./uam-staff-report.component.scss']
})
export class UamStaffReportComponent implements OnInit {
  staffList$: any;
  pdfData: any;

  constructor(
    private uamReportService: UamReportsService,
    private readonly loader: LoaderService
  ) { }

  ngOnInit() {
    this.staffList$ = this.uamReportService.getUAMStaff();
    this.setPageSizeOptions();
  }


  async exportTable(fileType) {
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "staffReportDumb",
        "staff_report"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      TableUtil.exportToExcel("staffReportDumb", "staff_report");
    }
  }

  setPageSizeOptions() {
    this.staffList$.subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              StaffName: key.StaffName,
              LastLogin: key.LastLogin,
              MobileAppPorter: key.IsPorter,
              Status: key.Status ? "Active" : "InActive",
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }
}
