import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { map } from "rxjs/operators";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: "root",
})
export class UamReportsService {
  constructor(private _httpClient: HttpClient) {}

  getUAMRoles() {
    return this._httpClient.get(`${environment.base_url}api/uam/roles`);
  }

  getUAMRolesModule() {
    return this._httpClient.get(`${environment.base_url}api/uam/roles/module`);
  }

  getUAMUsers() {
    return this._httpClient.get(`${environment.base_url}api/uam/users`);
  }

  getUAMStaff() {
    return this._httpClient.get(`${environment.base_url}api/uam/staff`);
  }

  getUserRoleDataMapping(userList$, allRolesDictionary) {
    return userList$.pipe(
      map((data: any) =>
        data.map((user) => {
          const rolesDictionary = { ...allRolesDictionary };
          rolesDictionary[user.role_name] = user.role_name;
          const roleUserMapping = Object.keys(rolesDictionary).map(
            (value) => rolesDictionary[value]
          );
          return {
            ...user,
            userRoleAssign: roleUserMapping,
          };
        })
      )
    );
  }
}
