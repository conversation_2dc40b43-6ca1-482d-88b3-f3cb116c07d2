<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
              <ul class="nav">
                <li class="nav">
                  <p *ngIf="!activatedRoute.snapshot.params.id">Add Job Type</p>
                  <p *ngIf="activatedRoute.snapshot.params.id">
                    Update Job Type
                  </p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="serviceRequests">
              <div class="row">
                <div class="col-12">
                  <form [formGroup]="subjobcategoryForm">
                    <fieldset class="scheduler-border">
                      <legend></legend>
                      <div class="row">
                        <div class="col-6 col__vertical__spacing">
                          <mat-form-field>
                            <mat-label>
                              Job Category
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <mat-select formControlName="mj_category_id">
                              <mat-option *ngFor="let service of masterService" [value]="service.mj_category_id">
                                {{ service.category_name }}</mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="
                                  subjobcategoryForm.controls.mj_category_id
                                " [fieldName]="'Job Category'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Standard Time Code
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <mat-select formControlName="std_time_code" (ngModelChange)="timeSelected($event)">
                              <mat-option *ngFor="let time of standardTime" [value]="time.std_id">
                                {{ time.std_code }}</mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="
                                  subjobcategoryForm.controls.std_time_code
                                " [fieldName]="'Standard Time Code'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-6 col__vertical__spacing">
                          <mat-form-field>
                            <mat-label>Job Type
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <input matInput placeholder="Job Type" formControlName="sj_category_name" />
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="
                                  subjobcategoryForm.controls.sj_category_name
                                " [fieldName]="'Job Type'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Priority
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <mat-select formControlName="priority">
                              <mat-option *ngFor="let val of subjobPriority" [value]="val.id">
                                {{ val.priority }}</mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="subjobcategoryForm.controls.priority"
                                [fieldName]="'Priority'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-6 col__vertical__spacing">
                          <mat-form-field>
                            <mat-label>Short Description
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <textarea matInput #desc placeholder="Descripton" maxlength="300"
                              formControlName="sj_short_discr" rows="1"></textarea>
                            <mat-hint style="text-align: end">{{ desc.value.length }} / 300
                            </mat-hint>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="
                                  subjobcategoryForm.controls.sj_short_discr
                                " [fieldName]="'Short Description'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Skill Level
                              <span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <mat-select formControlName="skill_level">
                              <mat-option *ngFor="let val of skillLevel" [value]="val">
                                {{ val }}</mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="
                                  subjobcategoryForm.controls.skill_level
                                " [fieldName]="'Skill Level'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>

                        <div class="col-6 col__vertical__spacing">
                          <mat-form-field>
                            <mat-label>Equipments<span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <mat-select formControlName="equipment_list" multiple="true">
                              <mat-option *ngFor="let val of modesOfTransport" [value]="val.transport_mode_id">
                                {{ val.transport_mode_name }}</mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="
                                  subjobcategoryForm.controls.equipment_list
                                " [fieldName]="'Equipments'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>

                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Start Time </mat-label>
                            <input matInput type="number" placeholder="Start Time" formControlName="start_time" />
                          </mat-form-field>

                          <mat-form-field>
                            <mat-label>End Time </mat-label>
                            <input matInput type="number" placeholder="End Time" formControlName="end_time" />
                          </mat-form-field>
                        </div>
                        <!-- <div class="col-6 h-0"></div> -->

                        <div class="col-6" style="top: -26px">
                          <mat-form-field>
                            <mat-label>No. Of Porters </mat-label>
                            <mat-select formControlName="no_of_porters">
                              <mat-option *ngFor="let val of porters" [value]="val" [attr.selected]="val">{{ val }}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Total Time </mat-label>
                            <input matInput type="number" placeholder="Total Time" formControlName="total_time" />
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Reason <span class="error-css">
                              </span>
                            </mat-label>
                            <textarea matInput #reason maxlength="300" placeholder="Reason" formControlName="reason"
                              rows="1"></textarea>
                            <mat-hint style="text-align: end;">{{reason.value.length}} / 300
                            </mat-hint>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="subjobcategoryForm.controls.reason" [fieldName]="'Reason'"
                                [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <section class="row p-2 no-gutters m-0 pb-4 w-100" style="border: 1.5px solid black">
                          <!-- Patient Move -->
                          <div class="col-6 mt-3">
                            <div class="row no-gutters m-0">
                              <div class="col-7" style="margin-top: 1%">
                                <mat-label style="float: left">
                                  <!-- Need Patient Info -->
                                  Is it a Patient Move
                                  <span class="error-css"><span class="error-css">*</span></span>
                                </mat-label>

                                <mat-radio-group formControlName="patient_info">
                                  <mat-radio-button class="example-margin" value="Yes">
                                    Yes
                                  </mat-radio-button>
                                  <mat-radio-button class="example-margin" value="No">
                                    No</mat-radio-button>
                                </mat-radio-group>
                                <mat-error class="pull-left error-css">
                                  <app-error-message [control]="
                                      subjobcategoryForm.controls.patient_info
                                    " [fieldName]="'Patient Info'" [fieldType]="'select'">
                                  </app-error-message>
                                </mat-error>
                              </div>
                            </div>
                          </div>
                          <!-- Gender -->
                          <!-- <div class="col-6 mt-3">
                            <div class="row no-gutters m-0">
                              <div class="col-12">
                                <div class="row no-gutters m-0">
                                  <div class="col-3 col-md-5">
                                    <mat-label style="float: left"
                                      >Required Gender<span class="error-css"
                                        ><span class="error-css">*</span></span
                                      >
                                    </mat-label>
                                  </div>
                                  <div class="col-7">
                                    <mat-radio-group
                                      formControlName="required_sex"
                                      style="margin-left: -31%"
                                    >
                                      <mat-radio-button
                                        class="example-margin"
                                        value="male"
                                      >
                                        Male
                                      </mat-radio-button>
                                      <mat-radio-button
                                        class="example-margin"
                                        value="Female"
                                      >
                                        Female</mat-radio-button
                                      >
                                      <mat-radio-button
                                        class="example-margin"
                                        value="others"
                                      >
                                        Any</mat-radio-button
                                      >
                                    </mat-radio-group>
                                  </div>
                                </div>
                              </div>
                              <div class="col-12">
                                <mat-error class="pull-left error-css">
                                  <app-error-message
                                    [control]="
                                      subjobcategoryForm.controls.required_sex
                                    "
                                    [fieldName]="'Gender'"
                                    [fieldType]="'select'"
                                  >
                                  </app-error-message>
                                </mat-error>
                              </div>
                            </div>
                          </div> -->
                          <!-- External Move -->
                          <div class="col-6 mt-3">
                            <div class="row no-gutters m-0">
                              <!-- <div class="col-3">
                                                                        <strong>SmartAssign Config</strong>
                                                                    </div> -->
                              <div class="col-12">
                                <mat-label style="float: left">
                                  <!-- External Move : -->
                                  Is it an External Move :
                                </mat-label>
                                <mat-checkbox class="align-checkbox" formControlName="external_move">
                                </mat-checkbox>
                              </div>

                              <!-- ///////////////////////////// -->

                              <!-- <div class="col-4">
                                <mat-label style="float: left">
                                  Ack required for this task :
                                </mat-label>
                                <mat-checkbox
                                  class="align-checkbox"
                                  formControlName="ack_req"
                                >
                                </mat-checkbox>
                              </div> -->

                              <!-- <div class="col-4">
                                <mat-label style="float: left"
                                  >
                                  Patient Info Required to create Task :
                                </mat-label>
                                <mat-checkbox
                                  class="align-checkbox"
                                  formControlName="patient_move"
                                >
                                </mat-checkbox>
                              </div> -->
                            </div>
                          </div>
                          <!-- Round Trip -->
                          <div class="col-6 mt-3">
                            <div class="row no-gutters m-0">
                              <div class="col-7">
                                <mat-label style="float: left">
                                  <!-- Round Trip -->
                                  Is it a Round Trip
                                </mat-label>
                                <mat-radio-group formControlName="round_trip">
                                  <mat-radio-button class="example-margin" value="Yes">
                                    Yes
                                  </mat-radio-button>
                                  <mat-radio-button class="example-margin" value="No">
                                    No</mat-radio-button>
                                </mat-radio-group>
                                <br />
                                <mat-error class="pull-left error-css">
                                  <app-error-message [control]="
                                      subjobcategoryForm.controls.round_trip
                                    " [fieldName]="'Round Trip'" [fieldType]="'select'">
                                  </app-error-message>
                                </mat-error>
                              </div>
                              <div class="col-7"></div>
                            </div>
                          </div>
                          <!-- Ack Required -->
                          <div class="col-6 mt-3">
                            <div class="row no-gutters m-0">

                              <div class="col-12">
                                <mat-label style="float: left">
                                  <!-- Ack Required : -->
                                  Ack required for this task :
                                </mat-label>
                                <mat-checkbox class="align-checkbox" formControlName="ack_req">
                                </mat-checkbox>
                              </div>
                            </div>
                          </div>
                          <!-- <div
                            class="col-6"
                            style="margin-top: -2px"
                            formGroupName="display_in"
                          >
                          </div> -->
                          <!-- Status -->
                          <div class="col-6">
                            <div class="row no-gutters ml-0">
                              <div class="col-7">
                                <mat-label style="float: left">Status :
                                </mat-label>
                                <mat-radio-group formControlName="status">
                                  <mat-radio-button class="example-margin" value="true">
                                    Active
                                  </mat-radio-button>
                                  <mat-radio-button class="example-margin" value="false">
                                    Inactive</mat-radio-button>
                                </mat-radio-group>
                                <mat-error class="pull-left error-css">
                                  <app-error-message [control]="
                                      subjobcategoryForm.controls.status
                                    " [fieldName]="'Status'" [fieldType]="'select'">
                                  </app-error-message>
                                </mat-error>
                              </div>
                            </div>
                          </div>
                          <!-- Patient Info Required -->
                          <div class="col-6 mt-3">
                            <div class="row no-gutters m-0">

                              <div class="col-12">
                                <mat-label style="float: left">
                                  <!-- Patient Move : -->
                                  Patient Info Required to create Task :
                                </mat-label>
                                <mat-checkbox class="align-checkbox" formControlName="patient_move">
                                </mat-checkbox>
                              </div>
                            </div>
                          </div>
                          <!-- Display In -->
                          <div class="col-6" style="margin-top: -2px" formGroupName="display_in">
                            <div class="row no-gutters m-0">
                              <div class="col-12">
                                <mat-label style="float: left">
                                  <!-- Display In -->
                                  This task needs to Display In
                                  <span class="error-css"><span class="error-css">*</span></span>
                                  :
                                </mat-label>

                                <section>
                                  <ul class="m-0">
                                    <li>
                                      <mat-checkbox class="example-margin" formControlName="normal">
                                        Normal</mat-checkbox>
                                    </li>
                                    <li>
                                      <mat-checkbox class="example-margin" formControlName="advance">
                                        Advance
                                      </mat-checkbox>
                                    </li>
                                    <li>
                                      <mat-checkbox formControlName="emergency">
                                        Urgent
                                      </mat-checkbox>
                                    </li>
                                  </ul>
                                </section>
                              </div>
                              <div class="col-12">
                                <mat-error class="pull-left error-css" style="margin-left: -4px"
                                  *ngIf="validationMessage.displayIn">
                                  {{ validationMessage.displayIn }}
                                </mat-error>
                              </div>
                            </div>
                          </div>
                          <!-- <div class="col-6"></div> -->
                          <!-- Display in Station -->
                          <div class="col-6">
                            <div class="row no-gutters m-0">
                              <div class="col-7">
                                <mat-label style="float: left">
                                  <!-- Display in Station : -->
                                  Is this task needs to Display in Station :
                                </mat-label>
                                <mat-checkbox class="align-checkbox" formControlName="display_in_station">
                                </mat-checkbox>
                                <!-- <mat-error class="pull-left error-css">
                                  <app-error-message
                                    [control]="
                                      subjobcategoryForm.controls
                                        .display_in_status
                                    "
                                    [fieldName]="'Display in Status'"
                                    [fieldType]="'select'"
                                  >
                                  </app-error-message>
                                </mat-error> -->
                              </div>
                            </div>
                          </div>
                           <!-- <div class="col-6"></div> -->
                          <!-- Skip Patient IC -->
                          <div class="col-6 mt-3" >
                            <div class="row no-gutters m-0">

                              <div class="col-12">
                                <mat-label style="float: left">
                                  <!-- Ack Required : -->
                                  Is Skip Patient IC allowed for this task :
                                </mat-label>
                                <mat-checkbox class="align-checkbox" formControlName="skip_patientic">
                                </mat-checkbox>
                              </div>
                            </div>
                          </div>
                          <!-- Urgent -->
                          <div class="col-6 mt-3">
                            <div class="row no-gutters m-0">
                              <div class="col-12" style="margin-top: 4px">
                                <mat-label style="float: left">
                                  <!-- Visible : -->
                                  Is Urgent allowed for this task :
                                </mat-label>
                                <section>
                                  <ul style="padding-left: 7px">
                                    <li>
                                      <mat-checkbox class="example-margin" formControlName="urgent">
                                        Urgent
                                      </mat-checkbox>
                                    </li>
                                    <!-- <li>
                                      <mat-checkbox formControlName="isolation_precaution">
                                        Isolation Precaution Required
                                      </mat-checkbox>
                                    </li> -->
                                  </ul>
                                </section>
                              </div>
                            </div>
                          </div>
                           <!-- Skip Ack -->
                           <div class="col-6 mt-3" >
                            <div class="row no-gutters m-0">

                              <div class="col-12">
                                <mat-label style="float: left">
                                  <!-- Ack Required : -->
                                  Is Skip Ack allowed for this task :
                                </mat-label>
                                <mat-checkbox class="align-checkbox" formControlName="skip_ack">
                                </mat-checkbox>
                              </div>
                            </div>
                          </div>
                        </section>
                        <div class="col-12 p-0">
                          <section class="row p-2 no-gutters m-0 mt-3 pb-5" style="border: 1.5px solid black">
                            <div class="col-6">
                              <div class="row no-gutters m-0 mt-3">
                                <div class="col-5">
                                  <mat-label style="float: left;">
                                    Stacking Allowed :
                                  </mat-label>
                                  <mat-checkbox class="align-checkbox" formControlName="SAStack">
                                  </mat-checkbox>
                                </div>
                                <div class="col-5">
                                  <mat-label style="float: left">Grouping Allowed :
                                  </mat-label>
                                  <mat-checkbox class="align-checkbox" formControlName="SAGroup">
                                  </mat-checkbox>
                                </div>
                              </div>
                            </div>

                            <div class="col-6">
                              <div class="row no-gutters m-0 mt-3">
                               <!-- <div class="col-6">
                                  <mat-label style="float: left">SmartAssign Allowed :
                                  </mat-label>
                                  <mat-checkbox class="align-checkbox" formControlName="SAAllowed">
                                  </mat-checkbox>
                                </div>-->
                                <div class="col-12">
                                  <div>
                                    <mat-label style="float: left">If a porter is doing this job, can stack
                                      another job
                                    </mat-label>
                                    <mat-checkbox class="align-checkbox" formControlName="SAStacktothis">
                                    </mat-checkbox>
                                  </div>
                                </div>
                              </div>
                            </div>
                             <!-- SmartAssign -->
                          <div class="col-6 mt-3" style="margin-top: -2px" formGroupName="SAAllowedJobs">
                            <div class="row no-gutters m-0">
                              <div class="col-12">
                                <mat-label style="float: left">
                                  <!-- SmartAssign -->
                                  SmartAssign Allowed Jobs :
                                </mat-label>

                                <section>
                                  <ul class="m-0">
                                    <li>
                                      <mat-checkbox class="example-margin" formControlName="Normal">
                                        Normal</mat-checkbox>
                                    </li>
                                    <li>
                                      <mat-checkbox class="example-margin" formControlName="Advance">
                                        Advance
                                      </mat-checkbox>
                                    </li>
                                    <li>
                                      <mat-checkbox formControlName="Emergency">
                                        Urgent
                                      </mat-checkbox>
                                    </li>
                                  </ul>
                                </section>
                              </div>
                              <div class="col-12">
                                <mat-error class="pull-left error-css" style="margin-left: -4px"
                                  *ngIf="validationMessage.displayIn">
                                  {{ validationMessage.displayIn }}
                                </mat-error>
                              </div>
                            </div>
                          </div>
                          </section>
                        </div>

                        <!-- <div class="col-6">
                                                    <mat-form-field>
                                                        <mat-label>Roles :
                                                        </mat-label>
                                                        <mat-select formControlName="role_id_list" multiple="true">
                                                            <mat-option *ngFor="let val of roles" [value]="val.role_id">
                                                                {{val.role_name}}</mat-option>
                                                        </mat-select>
                                                    </mat-form-field>
                                                </div> -->

                        <div class="col-6"></div>
                      </div>
                    </fieldset>
                  </form>
                  <div class="row from-submit">
                    <div class="col">
                      <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                        class="btn btn-primary pull-right" (click)="saveSubjobCategory('save')">
                        Submit
                      </button>
                      <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                        class="btn btn-primary pull-right" (click)="saveSubjobCategory('update')">
                        Update
                      </button>
                      <button mat-raised-button (click)="
                          subjobcategoryForm.reset();
                          subjobcategoryForm.controls.status.setValue('true');
                          getSubjobById()
                        " class="btn btn-white pull-right">
                        Reset
                      </button>
                    </div>
                  </div>
                  <!-- <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                    <legend class="scheduler-border-log">
                        Edit Logs
                    </legend>
                    <div class="row">
                        <div class="col-12">
                            <label class="col-4">Edited By</label>
                            <label class="col-4">Status</label>
                            <label class="col-4">Edited On</label>
                        </div>
                    </div>
                    <div class="row" *ngFor="let log of editLogs">
                        <div class="col-4">
                            {{log.edited_by}}
                        </div>
                        <div class="col-4">
                          {{log?.approval_status == 0 ? 'Pending' : log?.approval_status == 1 ? 'Approved' : 'Rejected'}}
                        </div>
                        <div class="col-4">
                            {{log.edited_date | localDateConversion: "full"}}
                        </div>
                    </div>
                </fieldset> -->
                  <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                    <legend class="scheduler-border-log">
                      Audit Logs
                    </legend>
                    <div class="mb-8">
                      <div class="logs-header row">
                        <div class="col-2 text-center">Job Type</div>
                        <div class="col-1 text-center">Status</div>
                        <div class="col-1 text-center">Approval Status</div>
                        <div class="col-2 text-center">Request By</div>
                        <div class="col-2 text-center">Request Date</div>
                        <div class="col-2 text-center">Approved By</div>
                        <div class="col-2 text-center">Approved Date</div>
                      </div>
                    </div>
                    <div class="" *ngFor="let log of editLogs; let i = index">
                      <div class="card card-xl-stretch">
                        <div class="card-body card-body-log pt-2 row m-2">
                          <div class="col-2 text-muted text-center fw-bold">{{log?.name || '--'}}</div>
                          <div class="col-1 text-muted text-center fw-bold" style="white-space: nowrap;">{{log?.status == "true" ? 'Active' :
                            'In-Active'}}</div>
                          <div class="col-1 text-muted text-center fw-bold" style="white-space: nowrap;">{{log?.approvalStatus || '--'}}</div>
                          <div class="col-2 text-muted text-center fw-bold">{{log?.created_By || '--'}}
                          </div>
                          <div class="col-2 text-muted text-center fw-bold">{{log?.created_Date ?
                            (log?.formattedCreatedDate) : '--'}}</div>
                          <div class="col-2 text-muted text-center fw-bold">{{log?.approved_By || '--'}}
                          </div>
                          <div class="col-2 text-muted text-center fw-bold">{{log?.approved_Date ?
                            (log?.formattedApprovedDate) : '--'}}</div>
                        </div>
                      </div>
                      <span *ngIf="i !== editLogs.length-1" style='font-size:35px;' class="text-muted">&#8593;</span>
                    </div>
                  </fieldset>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>