import {
  Component,
  ElementRef,
  OnInit,
  TemplateRef,
  ViewChild,
} from "@angular/core";
import {
  MatTableDataSource,
  MatPaginator,
  MatSort,
  MatDialog,
} from "@angular/material";
import { FormGroup, FormBuilder, FormControl } from "@angular/forms";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { EnhancedReport } from "src/app/models/enhancedReport";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { ToastrService } from "ngx-toastr";
import { ReportsService } from "../../../Apiservices/reports/reports.service";
import { InputValidationService } from "src/app/Apiservices/inputValidation/input-validation.service";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { Router } from "@angular/router";
import { JobsService } from "src/app/Apiservices/jobs/jobs.service";
import * as moment from "moment";
import { SettingsService } from "src/app/Apiservices/settings/settings.service";
import { map, startWith } from "rxjs/operators";
import { Observable } from "rxjs";

@Component({
  selector: "app-list-enhanced-report",
  templateUrl: "./list-enhanced-report.component.html",
  styleUrls: [
    "./list-enhanced-report.component.scss",
    "../../../scss/table.scss",
  ],
})
export class ListEnhancedReportComponent implements OnInit {
  routes = [
    { path: "./../enhancedReports", label: "Search (ACK)" },
    { path: "./../busrouteSearch", label: "Bus Route Reports" },
    { path: "./../porterReports", label: "Porter Workload" },
    { path: './../pts-reports', label: 'PTS Reports' },
    { path: './../enhanced-location-reports', label: 'Location Reports' }
  ];

  displayedColumns: string[] = [
    "order_no",
    "request_type",
    "order_from",
    "task_time",
    "assign_time",
    "start_time",
    "completion_time",
    "cancel_time",
    "requestor",
    "patient_name",
    "nric",
    "from_location",
    "from_room",
    "from_bed",
    "to_location",
    "to_room",
    "to_bed",
    "return_location",
    "return_bed",
    "remarks",
    "job_status",
    "main_category",
    "sub_category",
    "transport_mode",
    "std_kpi",
    "req_resp",
    "resp_comp",
    "within_kpi",
    "smart_assigned",
    "isolation_precaution",
    "resource1",
    "resource2",
    "created_date",
    "created_by",
    "modified_by",
    "modified_date",
    "assigned_by",
    "porterack_time",
    "scan_patient",
    "patient_scan_time",
    "delay_reason",
    "cancel_reason",
    "cancel_remarks",
    "cancelled_by"
  ];
  dataSource: MatTableDataSource<EnhancedReport>;
  exportResult: any;
  locationType = [];
  staffs = [];
  pdfData = [];
  showAck = false;
  statuss = [
    { condition: "Assigned" },
    { condition: "Completed" },
    { condition: "In Queue" },
    { condition: "Cancelled" },
    { condition: "Responded" },
  ];
  today = new Date().toUTCString();
  prevMonth = moment(this.today).subtract(1, "months");
  // tslint:disable-next-line: no-string-literal
  month = this.prevMonth["_d"];
  locations = [];
  mainJobCategories = [];
  modesOfTransport = [];
  subJobCategories = [];
  enhancedReportForm: FormGroup;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild("ackImageRef", { static: false })
  ackImageRef: TemplateRef<unknown>;

  mainCategoryCtrl = new FormControl();
  subCategoryCtrl = new FormControl();
  locationCtrl = new FormControl();
  modeOfTransportCtrl = new FormControl();
  statusCtrl = new FormControl();
  resourceCtrl = new FormControl();

  filteredCategory: Observable<any[]>;
  filteredSubCategory: Observable<any[]>;
  filteredLocation: Observable<any[]>;
  filteredModeOfTransports: Observable<any[]>;
  filteredStatus: Observable<any[]>;
  filteredResources: Observable<any[]>;
  FromDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "From Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
    readonly: true,
  };
  ToDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: false,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
    maxDateEnd: "",
    readonly: true,
  };
  mediaUrl: { type: string; url: string | ArrayBuffer };
  dialogRef: any;
  count = 0;
  constructor(
    private readonly fb: FormBuilder,
    private readonly toastr: ToastrService,
    public inputValidation: InputValidationService,
    private readonly loader: LoaderService,
    private readonly reports: ReportsService,
    private readonly userService: UserService,
    private readonly facilityConfig: FacilityConfigService,
    private readonly jobsServive: JobsService,
    public router: Router,
    private settingService: SettingsService,
    public dialog: MatDialog
  ) {
    this.createForm();
  }

  createForm() {
    this.enhancedReportForm = this.fb.group({
      nric: [""],
      from_date: [new Date()],
      to_date: [new Date()],
      order_no: [""],
      location: [""],

      main_category: [""],
      sub_category: [""],
      transport_mode: [""],
      status: [""],
      resource: [""],
      ack_required: [false],
    });
  }

  ngOnInit() {
    this.getLocations();
    this.getMainJobCategory();
    this.getStaff();
    this.getCurrentDateTime();
    this.filteredCategory = this.mainCategoryCtrl.valueChanges.pipe(
      startWith(""),
      map((category) => (category ? this._filterCategories(category) : this.mainJobCategories.slice()))
    );
    this.filteredSubCategory = this.subCategoryCtrl.valueChanges.pipe(
      startWith(""),
      map((category) => (category ? this._filteredSubCategory(category) : this.subJobCategories.slice()))
    );
    this.filteredLocation = this.locationCtrl.valueChanges.pipe(
      startWith(""),
      map((category) => (category ? this._filteredLocation(category) : this.locations.slice()))
    );
    this.filteredModeOfTransports = this.modeOfTransportCtrl.valueChanges.pipe(
      startWith(""),
      map((category) => (category ? this._filteredModeOfTransports(category) : this.modesOfTransport.slice()))
    );
    this.filteredStatus = this.statusCtrl.valueChanges.pipe(
      startWith(""),
      map((category) => (category ? this._filteredStatus(category) : this.statuss.slice()))
    );
    this.filteredResources = this.resourceCtrl.valueChanges.pipe(
      startWith(""),
      map((category) => (category ? this._filteredResources(category) : this.staffs.slice()))
    );
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.today = res;
      this.prevMonth = moment(this.today).subtract(1, "months");
      // tslint:disable-next-line: no-string-literal
      this.month = this.prevMonth["_d"];
      this.createForm();
      this.searchByData();
    });
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  _filterCategories(value: string) {
    const filterValue = value.toLowerCase();
    return this.mainJobCategories.filter(
      (category) => category.category_name.toLowerCase().indexOf(filterValue) === 0
    );
  }

  _filteredSubCategory(value: string) {
    const filterValue = value.toLowerCase();
    return this.subJobCategories.filter(
      (category) => category.sj_category_name.toLowerCase().indexOf(filterValue) === 0
    );
  }

  _filteredLocation(value: string) {
    const filterValue = value.toLowerCase();
    return this.locations.filter(
      (category) => category.location_name.toLowerCase().indexOf(filterValue) === 0
    );
  }

  _filteredModeOfTransports(value: string) {
    const filterValue = value.toLowerCase();
    return this.modesOfTransport.filter(
      (category) => category.transport_mode_name.toLowerCase().indexOf(filterValue) === 0
    );
  }

  _filteredStatus(value: string) {
    const filterValue = value.toLowerCase();
    return this.statuss.filter(
      (category) => category.condition.toLowerCase().indexOf(filterValue) === 0
    );
  }

  _filteredResources(value: string) {
    const filterValue = value.toLowerCase();
    return this.staffs.filter(
      (category) => category.staff_name.toLowerCase().indexOf(filterValue) === 0
    );
  }

  getStaff() {
    this.userService.getManageStaff().subscribe((res) => {
      res =
        res &&
        res.length &&
        res.filter((staff) => {
          if (staff.status) {
            return staff;
          }
        });
      this.staffs = res || [];
      this.filteredResources = this.resourceCtrl.valueChanges.pipe(
        startWith(""),
        map((category) => (category ? this._filteredResources(category) : this.staffs.slice()))
      );
    });
  }

  restForm() {
    this.paginator.pageIndex = 0;
    this.paginator.pageSize = 50;
    if (this.displayedColumns.includes("ack")) {
      this.displayedColumns.splice(this.displayedColumns.indexOf("ack"), 1);
    }
    this.subJobCategories.length = 0;
    this.createForm();
    this.searchByData();
  }

  getLocations() {
    this.facilityConfig.getLocations().subscribe((res) => {
      res =
        (res &&
          res.length &&
          res.filter((val) => {
            if (val.status) {
              return val;
            }
          })) ||
        [];
      this.locations = res;
      this.filteredLocation = this.locationCtrl.valueChanges.pipe(
        startWith(""),
        map((category) => (category ? this._filteredLocation(category) : this.locations.slice()))
      );
    });
  }
  getMainJobCategory() {
    this.enhancedReportForm.get("transport_mode").reset();
    this.enhancedReportForm.get("sub_category").reset();
    this.subJobCategories = [];
    this.modesOfTransport = [];
    this.facilityConfig.getMianJobCategory().subscribe((res) => {
      res =
        res &&
        res.length &&
        res.filter((vals) => {
          if (vals.status) {
            return vals;
          }
        });
      this.mainJobCategories = res || [];
      this.filteredCategory = this.mainCategoryCtrl.valueChanges.pipe(
        startWith(""),
        map((category) => (category ? this._filterCategories(category) : this.mainJobCategories.slice()))
      );
    });
  }
  getModesOfTransport(event) {
    this.modesOfTransport = [];
    this.enhancedReportForm.get("transport_mode").reset();
    if (event && this.enhancedReportForm.get("main_category").value) {
      this.jobsServive
        .getModesOfTransport(
          this.enhancedReportForm.get("main_category").value,
          event
        )
        .subscribe((res) => {
          if (
            res &&
            res.transport_mode_list &&
            res.transport_mode_list.length
          ) {
            this.modesOfTransport = res.transport_mode_list;
            this.filteredModeOfTransports = this.modeOfTransportCtrl.valueChanges.pipe(
              startWith(""),
              map((category) => (category ? this._filteredModeOfTransports(category) : this.modesOfTransport.slice()))
            );
          } else {
            this.enhancedReportForm.get("transport_mode").reset();
            this.toastr.info(
              "There is no modes of transport for selected main and sub job category",
              "Data not found"
            );
          }
        });
    }
    this.subCategoryCtrl.setValue("");
  }
  getsubJobCategory(id) {
    if (id && id !== '') {
      this.enhancedReportForm.get("transport_mode").reset();
      this.enhancedReportForm.get("sub_category").reset();
      this.subJobCategories = [];
      this.modesOfTransport = [];
      this.facilityConfig.getSubJobCategoryMainJobBased(id).subscribe((res) => {
        res =
          (res &&
            res.length &&
            res.filter((data) => {
              if (data.status) {
                return data;
              }
            })) ||
          [];
        if (res && res.length) {
          this.subJobCategories = res;
          this.filteredSubCategory = this.subCategoryCtrl.valueChanges.pipe(
            startWith(""),
            map((category) => (category ? this._filteredSubCategory(category) : this.subJobCategories.slice()))
          );
        } else {
          this.enhancedReportForm.get("transport_mode").reset();
          this.enhancedReportForm.get("sub_category").reset();
          this.toastr.info(
            "There is no sub job categories for selected main job category",
            "Data not found"
          );
        }
      });
    }
    this.mainCategoryCtrl.setValue("");
  }
  async exportTable(fileType) {
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "enhancedReport",
        "enhancedReport_list",
        310
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      if (this.enhancedReportForm.get("ack_required").value === true) {
        this.showAck = true;
        this.searchByData(fileType);
        // TableUtil.exportArrayToExcel(this.exportResult,this.showAck,"enhancedReport_list");

      } else {
        this.showAck = false;
        this.searchByData(fileType);
        // TableUtil.exportArrayToExcel(this.exportResult,this.showAck,"enhancedReport_list");

      }
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Job No": key.order_no,
              "Req-Resp(mints)": key.req_resp,
              "Resp-Comp(mints)": key.resp_comp,
              "Request Type": key.request_type,
              "Creation Time": key.created_date,
              "Task Time": key.task_time,
              "Assigned Time": key.assign_time,
              "Start Time": key.start_time,
              "Completion Time": key.completion_time,
              "Cancel Time": key.cancel_time,
              Requestor: key.requestor,
              NRIC: key.nric,
              Patient: key.patient_name,
              "Order From": key.order_from,
              From: key.from_location,
              "Room No": key.from_room,
              "Bed No": key.from_bed,
              To: key.to_location,
              "Room Number": key.to_room,
              "Bed Number": key.to_bed,
              "Job Status": key.job_status,
              "Main Category": key.main_category,
              "Sub Category": key.sub_category,
              "Transport Mode": key.transport_mode,
              "Smart Assigned": key.smart_assigned,
              "Isolation Precaution": key.isolation_precaution,
              Remarks: key.remarks,
              "Resource 1": key.resource1,
              "Resource 2": key.resource2,
              "Created By": key.created_by,
              "Modified By": key.modified_by,
              "Modified Date": key.modified_date,
              "Delay Reason": key.delay_reason,
              "Cancel Reason": key.cancel_reason,
              ACK: key.ack,
              ...key,
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  searchByData(type?, paginationData?) {
    const data: any = this.userService.getOnlyFilledObjects(
      this.enhancedReportForm.value
    );
    const fromdate = moment(
      this.enhancedReportForm.get("from_date").value
    ).format("YYYY/MM/DD");
    const todate = moment(this.enhancedReportForm.get("to_date").value).format(
      "YYYY/MM/DD"
    );
    if (data.status) {
      if (data.status == 'In Queue') {
        data.status = 'INQ'
      } else if (data.status == 'Responded') {
        data.status = 'Started'
      }
    }
    data.from_date = fromdate;
    data.to_date = todate;
    data.pageNo = type && type == 'next' ? Number(paginationData.offset) + 1 : 1;
    data.pageSize = (type && type == 'next' ? Number(paginationData.limit) : type && (type == 'csv' || type == 'xlsx') ? this.count : this.paginator.pageSize) || 50;
    if (
      (moment(data.from_date).isValid() && moment(data.to_date).isValid()) ||
      data.order_no
    ) {
      this.reports.getEnhancedReportSearch(data).subscribe(
        (res) => {
          if (res && res.length > 0) {
            if (type && type == 'next') {
              this.exportResult = res;
              this.exportResult.filter(result => {
                if (result.request_type && (result.request_type === 'Normal' || result.request_type === 'Urgent')) {
                  result['task_time'] = result.created_date || null;
                } else {
                  result['task_time'] = result.due_time || null;
                }
              })
              debugger;
              this.exportResult.length = paginationData.currentSize == 0 ? res[0].count : paginationData.currentSize;
              this.exportResult.push(...res);
              this.exportResult.length = res[0].count;
              this.dataSource = new MatTableDataSource<any>(this.exportResult);
              this.dataSource._updateChangeSubscription();
              this.dataSource.paginator = this.paginator;
            } else if (type && (type == 'xlsx' || type == 'csv')) {
              this.exportResult = res;
              TableUtil.exportArrayToExcel(this.exportResult, this.showAck, "enhancedReport_list", type);
            } else {
              this.exportResult = res;
              this.exportResult.filter(result => {
                if (result.request_type && (result.request_type === 'Normal' || result.request_type === 'Urgent')) {
                  result['task_time'] = result.created_date || '';
                } else {
                  result['task_time'] = result.due_time || '';
                }
              })
              this.paginator.pageIndex = 0
              this.exportResult.length = res[0].count;
              this.count = res[0].count;
              this.paginator.length = res[0].count;
              this.dataSource = new MatTableDataSource(this.exportResult || []);
              console.log(this.dataSource.data)
              this.dataSource.sort = this.sort;
              this.dataSource.paginator = this.paginator;
              this.setPageSizeOptions();
            }
            if (this.enhancedReportForm.get("ack_required").value === true) {
              this.showAck = true;
              if (this.displayedColumns.includes("ack") === false) {
                this.displayedColumns.push("ack");
              }
            } else {
              if (this.displayedColumns.includes("ack")) {
                this.displayedColumns.splice(
                  this.displayedColumns.indexOf("ack"),
                  1
                );
              }
            }
          } else {
            this.paginator.length = 0;
            this.dataSource = new MatTableDataSource([]);
          }
        },
        (err) => {
          this.paginator.length = 0;
          this.dataSource = new MatTableDataSource([]);
        }
      );
      //  this.getExportXlsData(data);
    } else {
      this.toastr.warning("Please have either by job No. or From & To date");
    }
  }
  getDate() {
    if (
      this.enhancedReportForm.get("from_date").value &&
      this.enhancedReportForm.get("to_date").value
    ) {
      if (
        this.enhancedReportForm.get("from_date").value >
        this.enhancedReportForm.get("to_date").value
      ) {
        this.enhancedReportForm.get("to_date").setValue("");
        this.toastr.error("To date should be greater then From date", "Error");
      }
    }
  }

  pageChanged(event) {
    let pageIndex = event.pageIndex;
    let pageSize = event.pageSize;
    let previousIndex = event.previousPageIndex;
    let previousSize = pageSize * pageIndex;
    this.getNextData(previousSize, (pageIndex).toString(), pageSize.toString());
  }

  getNextData(currentSize, offset, limit) {
    const paginationData = {
      'currentSize': currentSize,
      'offset': offset,
      'limit': limit
    }
    this.searchByData('next', paginationData);
  }

  downloadHelpFile(id, method) {
    this.mediaUrl = { type: "image", url: "" };
    this.settingService.downloadAckReportFile(id).subscribe(
      (res) => {
        if (method == "video" || method == "image") {

          let reader = new FileReader();
          reader.readAsDataURL(res.body);
          setTimeout(() => {
            this.mediaUrl = { url: reader.result, type: res.body["type"] };
          }, 1000);

        }
        this.openAckImageViewDialog();
      },
      (err) => {

      }
    );
  }

  openAckImageViewDialog() {
    this.dialogRef = this.dialog.open(this.ackImageRef, {
      width: "40vw",
      height: "auto",
    });
  }
  checkBrowserInstance() {
    if (
      !!navigator.userAgent.match(/Trident.*rv\:11\./) ||
      !!document["documentMode"]
    ) {
      return true;
    } else {
      return false;
    }
  }

  clearFormValue(formField: string) {
    this.enhancedReportForm.get([formField]).setValue('')
  }
}
