@import "../../../../assets/scss/navbar.scss";

.main-content {
  width: 100%;
  margin: 10px auto;
}
.mat-radio-button ~ .mat-radio-button {
  // margin-left: 16px;
}

// common css
::ng-deep .mat-select-panel {
  min-width: inherit !important;
  max-width: inherit !important;
}
::ng-deep .mat-radio-label-content {
  font-weight: 600;
  color: black;
}
::ng-deep .mat-checkbox-layout {
  font-weight: 600;
  color: black;
}
::ng-deep .mat-form-field-label {
  font-weight: 600 !important;
  color: black !important;
}
::ng-deep .mat-checkbox-label {
  color: #000;
}

::ng-deep .mat-form-field-label-wrapper {
  font-size: 13px !important;
}
// common css
.col-6 {
  flex: 0 0 100% !important;
}

button {
  cursor: pointer;
}
.nav-tabs-wrapper {
  display: inline-flex;
  em {
    float: left;
    cursor: pointer;
    margin-top: 10px;
  }
}
.nav li {
  width: auto;
}

.modal-body {
  overflow-x: auto;
}

.arraytableError {
  font-size: 11px;
}

.tbl tbody {
  width: 100%;
  overflow: auto;
}

.btn-xs {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

.toggle-example {
  position: relative;
  width: 100%;
  height: 36px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}

.example-full-width {
  width: 100%;
}

.example-option-img {
  vertical-align: middle;
  margin-right: 8px;
}

[dir="rtl"] .example-option-img {
  margin-right: 0;
  margin-left: 8px;
}

.example-container {
  height: 200px;
  width: 100%;
  overflow: auto;
}

table {
  border: 1px solid #ccc;
  border-collapse: collapse;
  margin: 0;
  padding: 0;
  width: 100%;
  table-layout: fixed;
}

table caption {
  font-size: 1em;
  margin: 0.25em 0 0.5em;
}

.example-margin {
  margin: 0 10px 0 0;
}

table tr {
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  padding: 0.2em;
}

table th,
table td {
  padding: 0.5em;
  text-align: center;
}

table th {
  font-size: 12px;
  line-height: 1.42857;
  color: black;
  font-weight: 400;
}

@media screen and (max-width: 600px) {
  table {
    border: 0;
  }

  table caption {
    font-size: 1.3em;
  }

  table thead {
    border: none;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }

  table tr {
    border-bottom: 3px solid #ddd;
    display: block;
    margin-bottom: 0.625em;
  }

  table td {
    border-bottom: 1px solid #ddd;
    display: block;
    font-size: 0.8em;
    text-align: right;
  }

  table td::before {
    content: attr(data-label);
    float: left;
  }

  table td:last-child {
    border-bottom: 0;
  }
}

.mat-option-text {
  font-size: 14px !important;
}

fieldset.scheduler-border {
  border: 0.8px groove #ddd !important;
  padding: 0 1em 1em 1em !important;
  margin: 0 0 1.5em 0 !important;
  -webkit-box-shadow: 0px 0px 0px 0px #000;
  box-shadow: 0px 0px 0px 0px #000;
  height: 95.6%;
}

legend.scheduler-border {
  font-weight: normal !important;
  color: darkblue;
  font-size: 13px;
  text-align: left !important;
  width: auto;
  padding: 0 10px;
  border-bottom: none;
}

legend {
  display: block;
}
.cancelled-card {
  padding: 10px;
  border-radius: 5px;
}

.checkbox {
  margin-left: 20px;
  margin-right: 20px;
  margin-top: 2px;
  float: left;
}

.border__requestor {
  color: #3f51b5;
  text-align: left;
  font-weight: 600;
}

.or__feature {
  margin-top: 2%;
  text-align: center;
}
.mat__chip {
  cursor: pointer;
  margin-left: 6% !important;
  height: auto;
  span {
    padding-left: 4px;
    font-size: 12px;
  }
}
.mat-chip-active_value {
  background-color: #ffa500 !important;
  color: #ffffff;
}
::ng-deep .mat-chip.mat-standard-chip {
  background-color: #ebecf0;
}

.font__weight__600 {
  font-weight: 600;
}
.font__weight__500 {
  font-weight: 500;
}
.important__note {
  font-size: 12px;
  color: #88888b;
}
.card__custom__header {
  width: 100%;
  /* line-height: 61px; */
  padding-top: 04px;
  font-size: 15px;
  font-weight: 600;
  p {
    color: #3f51b6;
  }
}
.mat__expansion__panel__custom {
}

// this is ngx timepicker css
::ng-deep .ngx-timepicker-control__arrow {
  display: none;
}

::ng-deep .ngx-timepicker {
  position: relative;
  top: -15px;
  align-items: flex-end !important;
}

::ng-deep .ngx-timepicker-control {
  width: 32px !important;
  height: 23px !important;
  padding: 0 0px !important;
}
::ng-deep .ngx-timepicker__time-colon {
  margin-left: 0px !important;
}
::ng-deep .period-control__button {
  width: auto !important;
  font-size: 0.9rem !important;
}

.time__picker {
  height: 100%;
  margin-top: 4%;
}

.tab { 
  display:inline-block; 
  margin-left: 40px; 
}

.align-arrow-right {
  position: absolute;
  right: 0; //change as per requirement
  top: 0; //change as per requirement
}

.make_label {
  color: black !important;
  font-weight: 600 !important;
}

.separator {
  margin-top: 20px;
  font-weight: 600;
  text-align: center;
  color: #3f51b5;
  font-size: 12px;
}