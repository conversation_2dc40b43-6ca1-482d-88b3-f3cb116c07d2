import { Component, OnInit } from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { StorageService } from "src/app/Apiservices/stoargeService/storage.service";
import { Router } from "@angular/router";

@Component({
  selector: "app-login",
  templateUrl: "./login.component.html",
  styleUrls: ["./login.component.scss"],
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  logoUrl = "";
  domainList: any;

  constructor(
    private readonly fb: FormBuilder,
    private readonly toastr: ToastrService,
    private readonly userService: UserService,
    private readonly storageService: StorageService,
    private readonly router: Router
  ) {
    this.loginForm = this.fb.group({
      username: ["", [Validators.required]],
      password: ["", Validators.required],
      ad_url: ["", Validators.required],
    });
  }

  ngOnInit() {
    this.storageService.removeAllData();
    this.getLogo();
    this.checkISldap();
  }

  getLogo() {
    this.userService.logoUrl.subscribe((res) => {
      this.logoUrl = res || "";
    });
  }

  login() {
    if (this.loginForm.valid) {
      this.storageService.removeAllData();
      this.userService.login(this.loginForm.value).subscribe(
        (res) => {
          if (res.token) {
            this.storageService.setData("access_token", res.token);
            this.storageService.setData("locationid", res.location);
            this.storageService.setData("contact_no", res.contact_no);
            this.storageService.setData("user_id", btoa(res.id));
            this.storageService.setData("global_timer_toggle", btoa("false"));
            this.router.navigateByUrl("/app/dashboard");
          }
        },
        (err) => {
          console.log(err);
        }
      );
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.loginForm.markAllAsTouched();
    }
  }

  checkISldap() {
    this.userService.getAds().subscribe((res) => {
      this.userService.isldap.next((res && res.ad_status) || false);
      this.userService.logoUrl.next((res && res.ad_image) || "");
      this.domainList = res.ad_settings;
      if (this.domainList.length) {
        this.loginForm.get("ad_url").patchValue(this.domainList[0].ad_url);
      }
    });
  }
}
