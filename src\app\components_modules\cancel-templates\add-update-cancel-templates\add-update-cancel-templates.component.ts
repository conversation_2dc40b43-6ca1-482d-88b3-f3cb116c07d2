import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { SettingsService } from 'src/app/Apiservices/settings/settings.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-add-update-cancel-templates',
  templateUrl: './add-update-cancel-templates.component.html',
  styleUrls: ['./add-update-cancel-templates.component.scss']
})
export class AddUpdateCancelTemplatesComponent implements OnInit {
  messageForm: FormGroup;
  editLogs: any = [];

  constructor(
    private readonly fb: FormBuilder,
    public location: Location,
    public activatedRoute: ActivatedRoute,
    private readonly toastr: ToastrService,
    private readonly settingService: SettingsService
  ) {
    this.messageForm = this.fb.group({
      cancel_template: ['', Validators.required],
      allow_remarks: 0,
      reason: [''],
      approval_status: ['']
    });
  }

  ngOnInit() {
    this.getCancelTemplate();
  }

  getCancelTemplate() {
    if (this.activatedRoute.snapshot.params.id) {
      this.settingService.getCanceltemplate(this.activatedRoute.snapshot.params.id).subscribe(res => {
        this.editLogs = res.edit_logs || [];
        this.editLogs.sort(function (a: any, b: any) {
          let B: any = new Date(b.created_Date);
          let A: any = new Date(a.created_Date);
          return B - A;
        });
        this.editLogs.map(log => {
          log['formattedCreatedDate'] = log && log.created_Date ? log.created_Date.split('/').join("-") : null
            log['formattedApprovedDate'] = log && log.approved_Date ? log.approved_Date.split('/').join("-") : null
          log['approvalStatus'] = log.approval_Status == '0' ? 'Pending' : log.approval_Status == '1' ? 'Approved' : log.approval_Status == '2' ? 'Rejected' : '--'
        })
        this.messageForm.patchValue(res ? res : {});
      }, err => {
        this.toastr.error('Error occured in fetching cancel template', 'Error');
      });
    }
  }


  saveCancelTemplate(actiontype) {
    const data = this.messageForm.value;
    data.status = 1;
    data.allow_remarks = data.allow_remarks === true || data.allow_remarks === 1 || data.allow_remarks === '1' ? 1 : 0;
    if (this.messageForm.valid) {
      if (data.approval_status && data.approval_status == 'Pending') {
        Swal.fire({
          title: 'This request is already under process',
          text: `Are you sure, You want to update?`,
          icon: 'warning',
          showCancelButton: true,
          cancelButtonText: 'No',
          confirmButtonText: 'Yes'
        }).then((result) => {
          if (result.value) {
            this.addUpdateCanceltemplate(data, actiontype);
          } else if (result.dismiss === Swal.DismissReason.cancel) {
            return;
          }
        });
      } else {
        this.addUpdateCanceltemplate(data, actiontype);
      }
    } else {
      this.toastr.warning('Please enter all highlighted fields', 'Validation failed!');
      this.messageForm.markAllAsTouched();
    }
  }

  addUpdateCanceltemplate(data, actiontype) {
    this.settingService.addUpdateCanceltemplate(data, actiontype, this.activatedRoute.snapshot.params.id).subscribe(() => {
      this.location.back();
      this.toastr.success(`Successfully ${actiontype === 'add' ? 'added' : 'updated'} cancel template message`, 'Success');
    }, err => {
      console.log(err);
    });

  }

}
