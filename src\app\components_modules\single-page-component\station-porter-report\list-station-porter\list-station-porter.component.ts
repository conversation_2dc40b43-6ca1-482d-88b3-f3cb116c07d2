import { Component, OnInit, ViewChild } from "@angular/core";
import { MatTableDataSource, MatPaginator, MatSort } from "@angular/material";
import { TableUtilPDf } from "src/app/utuilities/tablePdfUtility";
import { TableUtil } from "src/app/utuilities/tablexlsxutility/tableXslxUtility";
import { StationReport } from "src/app/models/stationReport";
import { LoaderService } from "src/app/Apiservices/loader/loader.service";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import { FacilityConfigService } from "src/app/Apiservices/facilityConfig/facility-config.service";
import { UserService } from "src/app/Apiservices/userService/user.service";
import { ReportsService } from "src/app/Apiservices/reports/reports.service";
import * as moment from "moment";
import { FormControl } from "@angular/forms";
import { ReplaySubject, Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";

@Component({
  selector: "app-list-station-porter",
  templateUrl: "./list-station-porter.component.html",
  styleUrls: [
    "./list-station-porter.component.scss",
    "../../../../scss/table.scss",
  ],
})
export class ListStationPorterComponent implements OnInit {
  routes = [
    // { path: './../stationReport', label: 'Station Porter Reports' },
    // { path: './../stationSpecificReport', label: 'Station Specific Porter Reports' },
  ];
  displayedColumns: string[] = [
    "JobID",
    "station_location",
    "staff_name",
    "FromLocation",
    "ToLocation",
    "JobType",
    "TransportType",
    "RequestTime",
    "StartTime",
    "CompletionTime",
    "CancelTime",
    "PatientName_NRICNO",
    "PatientICScanTime",
    "Remarks"
  ];
  dataSource: MatTableDataSource<StationReport>;
  public locationFilterCtrl: FormControl = new FormControl();
  stationReportForm: FormGroup;
  report = [];
  reportData = [
    { label: "Specific Date Report", id: "specificdate" },
    { label: "Date Range Report", id: "daterange" },
  ];
  specificDate = {
    rangePicker: false,
    collumns: "col-16",
    label: "Specific Date",
    required: true,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
  };
  Todate = {
    rangePicker: false,
    collumns: "col-16",
    label: "To Date",
    required: true,
    minDateStart: "2000-12-12",
    maxDateStart: "",
    minDateEnd: "",
  };
  pdfData = [];
  locations = [];
  staff = [];
  viewData: any;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  today = new Date();
  currentYear = this.today.getFullYear();
  // prevMonth = moment(this.today).subtract(1, "months");
  prevMonth = moment(this.today)
  // tslint:disable-next-line: no-string-literal
  month = this.prevMonth["_d"];
  locationsList: any = [];
  exportResult = [];
  count = 0;
  public filteredLocations: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  constructor(
    private readonly loader: LoaderService,
    private readonly fb: FormBuilder,
    private readonly toastr: ToastrService,
    private readonly facilityConfig: FacilityConfigService,
    private readonly userService: UserService,
    private readonly reports: ReportsService
  ) {
    this.stationForm();
  }
  stationForm() {
    this.stationReportForm = this.fb.group({
      report_type: ["specificdate"],
      locationsList: [0],
      locationFilterCtrl: [0],
      staff: [0, Validators.required],
      from: [this.month, Validators.required],
      to: [this.month, Validators.required],
    });
  }

  getCurrentDateTime() {
    this.userService.currentDatetime().subscribe((res) => {
      this.today = res;
      this.prevMonth = moment(this.today)
      // .subtract(1, "months");
      // tslint:disable-next-line: no-string-literal
      this.month = this.prevMonth["_d"];
      this.stationForm();
    });
  }

  ngOnInit() {
    this.dataSource = new MatTableDataSource([]);
    this.getLocations();
    this.getCurrentDateTime();
    // real time search location
    this.filteredLocations.next(this.locationsList.slice());
    this.locationFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterLocations();
      });
      this.getManageStaff();
  }

  filterLocations() {
    if (!this.locationsList) {
      return;
    }
    // get the search keyword
    let search = this.locationFilterCtrl.value;
    // DON'T REMOVE THIS CODE
    // this.taskRequest.fromLocationId =
    //   this.locations.length > 1 ? this.locations.slice()[0].id : "";
    if (!search) {
      this.filteredLocations.next(<any[]>this.locationsList.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.filteredLocations.next(
      this.locationsList.filter(
        (x) => x.location_name.toLowerCase().indexOf(search) > -1
      )
    );

  }
  getLocations() {
    this.facilityConfig.getLocations().subscribe((res) => {
      res =
        (res &&
          res.length &&
          res.filter((val) => {
            if (val.status) {
              return val;
            }
          })) ||
        [];
      this.locationsList = res;
      this.filterLocations();

    });
  }
  
  getStaff() {
    this.userService.getMessageStaffActive().subscribe((res) => {
      this.staff = res;
    });
  }
  getManageStaff() {
    this.userService.getManageStaff().subscribe((res) => {
      if (res) {
        // this.staff = res;
        res.filter(s => {
          if (s && s.status) {
            this.staff.push(s)
          }
        })
      }  
    });
  }
  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
    this.setPageSizeOptions();
  }
  async exportTable(fileType) {
    this.searchStationReport(fileType);
    window.scrollTo(0, 0);
    if (fileType === "pdf") {
      this.loader.show();
      const genrated = await TableUtilPDf.exportToPdf(
        "stationReportTable",
        "stationReport_list"
      );
      if (genrated) {
        this.loader.hide();
      }
    } else {
      
    }
  }

  mapTableDataforReport(exportResult, type?) {
    let mappedData;
    let exportObj: any[]=[];
    for (var i=0;i<exportResult.length;i++) {
      if (exportResult[i]) {
        mappedData = {
          "Order No":exportResult[i].JobID,
          "Station Location":exportResult[i].station_location ? exportResult[i].station_location.toString().replace("\r\n","") : "--",
          "Staff Name":exportResult[i].staff_name,
          "From":exportResult[i].FromLocation ? exportResult[i].FromLocation.toString().replace("\r\n","") : "--",
          "To":exportResult[i].ToLocation ? exportResult[i].ToLocation.toString().replace("\r\n","") : "--",
          "Job Type":exportResult[i].JobType,
          "Transportation Type":exportResult[i].TransportType,
          "Request Time":exportResult[i].RequestTime !== '' ? exportResult[i].RequestTime : '--',
          "Respond Time":exportResult[i].StartTime !== ''? (exportResult[i].StartTime) : '--',
          "Completion Time":exportResult[i].CompletionTime !== '' ? (exportResult[i].CompletionTime) : '--',
          "Cancel Time":exportResult[i].CancelTime !== '' ? (exportResult[i].CancelTime) : '--',
          "Patient Name/NRIC No":exportResult[i].PatientName_NRICNO,
          "Patient IC Scanned Time":exportResult[i].PatientICScanTime,
          "Remarks":exportResult[i].Remarks,
        };
        exportObj.push(mappedData);
      }
    }
    if(type == 'xsls') {
      TableUtil.exportArrayToExcelDynamically(exportObj, "stationReport_list");
    } else if (type == 'csv') {
      TableUtil.exportArrayToCsvDynamically(exportObj, "stationReport_list");
    }
  }

  setPageSizeOptions() {
    this.dataSource.connect().subscribe((data) => {
      this.pdfData =
        (data &&
          data.filter((key) => {
            const a = {
              "Order No": key.JobID,
              "Station Location": key.station_location,
              "Staff Name": key.staff_name,
              From: key.FromLocation,
              To: key.ToLocation,
              "Job Type": key.JobType,
              "Transportation Type": key.TransportType,
              "Request Time": key.RequestTime,
              "Respond Time": key.StartTime,
              "Completion Time": key.CompletionTime,
              "Cancel Time": key.CancelTime,
              "Patient Name/NRIC No": key.PatientName_NRICNO,
            };
            Object.assign(key, a);
            return key;
          })) ||
        [];
    });
  }

  searchStationReport(type?) {
    if (this.stationReportForm.valid) {
      const data = this.stationReportForm.value;
      data.from = moment(data.from).format("YYYY-MM-DD");
      data.to = moment(data.to).format("YYYY-MM-DD");
      if (data.report_type === "specificdate") {
        data.to = data.from;
      }
      // if(!type){
        this.paginator.pageIndex = 0;
      // }
      this.paginator._intl.itemsPerPageLabel
      data.pageNo = (this.paginator.pageIndex + 1) || 1;
      data.pageSize = type && (type == 'xsls' || type == 'csv') ? this.count : this.paginator.pageSize > 50 ? this.paginator.pageSize : 50;
      this.reports
        .searchStationReport(data)
        .subscribe((res) => {
          if(res && res.length > 0) {
            this.exportResult = res;
            this.exportResult.length = res[0].count;
            this.count = res[0].count;
            this.dataSource = new MatTableDataSource(this.exportResult || []);
            this.dataSource.sort = this.sort;
            this.dataSource.paginator = this.paginator;
            this.dataSource._updateChangeSubscription();
            this.setPageSizeOptions();
            if (type && (type == 'xsls' || type == 'csv')) {
              this.mapTableDataforReport(this.exportResult, type)
            }
          } else {
            if (type && (type == 'xsls' || type == 'csv')) {
              this.mapTableDataforReport(this.exportResult, type)
            }
            this.paginator.length = 0;
            this.dataSource = new MatTableDataSource([]);
          }
        }, () => {
          this.paginator.length = 0;
          this.dataSource = new MatTableDataSource([]);
        });
    } else {
      this.toastr.warning(
        "Please enter all highlighted fields",
        "Validation failed!"
      );
      this.stationReportForm.markAllAsTouched();
    }
  }

  pageChanged(event){
    let pageIndex = event.pageIndex;
    let pageSize = event.pageSize;
    let previousIndex = event.previousPageIndex;
    let previousSize = pageSize * pageIndex;
    this.getNextData(previousSize, (pageIndex).toString(), pageSize.toString());
  }

  getNextData(currentSize, offset, limit){
    if (this.stationReportForm.valid) {
      const data = this.stationReportForm.value;
      data.from = moment(data.from).format("YYYY-MM-DD");
      data.to = moment(data.to).format("YYYY-MM-DD");
      if (data.report_type === "specificdate") {
        data.to = data.from;
      }
      data.pageNo = Number(offset) + 1;
      data.pageSize = Number(limit);
      this.reports.searchStationReport(data).subscribe((res) => {
        if(res && res.length > 0){
          this.exportResult = res;
          this.exportResult.length = currentSize == 0 ? res[0].count : currentSize;
          this.exportResult.push(...res);
          this.exportResult.length = res[0].count;
          this.dataSource = new MatTableDataSource<any>(this.exportResult);
          this.dataSource._updateChangeSubscription();
          this.dataSource.paginator = this.paginator;
        }
      })
    }
  }

  reset() {
    this.stationReportForm.reset();
    this.stationForm();
    this.paginator.length = 0;
    this.dataSource = new MatTableDataSource([]);
  }
  reportTypeChange() {
    if (this.stationReportForm.get("report_type").value === "daterange") {
      this.specificDate.label = "From Date";
    } else {
      this.specificDate.label = "Specific Date";
    }
  }

  clearFormValue(formField: string) {
    this.stationReportForm.get([formField]).setValue("");
  }
}
