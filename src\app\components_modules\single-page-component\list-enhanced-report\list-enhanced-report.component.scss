@import "../../../../assets/scss/navbar.scss";
td,
th {
  min-width: 194px;
  width: 10%;
}
th {
  min-width: 194px;
  width: 10em;
}
fieldset.scheduler-border {
  border: 0.8px groove #ddd !important;
  padding: 0 1em 1em 1em !important;
  margin: 0 0 1.5em 0 !important;
  -webkit-box-shadow: 0px 0px 0px 0px #000;
  box-shadow: 0px 0px 0px 0px #000 !important;
  height: 350px;
}

legend.scheduler-border {
  font-size: 1em !important;
  font-weight: normal !important;
  color: darkblue;
  text-align: left !important;
  width: auto;
  padding: 0 10px;
  border-bottom: none;
}
legend {
  display: block;
}
.mat-form-field {
  height: 60px !important;
}
.alignInput {
  float: left;
  display: inline-flex;
  mat-checkbox {
    padding-right: 20px;
  }
}
.search .btn-primary {
  margin-right: 10px;
}
::ng-deep .alignSelectBox .mat-form-field-infix {
  padding-top: 2px !important;
}

.subList {
  margin-left: 40px;
  font-weight: bold;
  .btn {
    margin-right: 20px;
  }
  .btn.btn-primary {
    width: 170px !important;
  }
}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle {
  color: #ffffff;
  background-color: #999999;
  border-color: #999999;
}

.spacing {
  margin-bottom: 12px;
}
