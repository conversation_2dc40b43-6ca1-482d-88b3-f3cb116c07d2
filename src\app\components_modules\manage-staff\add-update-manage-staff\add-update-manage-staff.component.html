<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="nav-tabs-wrapper">
              <em class="material-icons" (click)="location.back()">keyboard_backspace</em>
              <ul class="nav">
                <li class="nav">
                  <p *ngIf="!activatedRoute.snapshot.params.id">
                    Add Manage Staff
                  </p>
                  <p *ngIf="activatedRoute.snapshot.params.id">
                    Update Manage Staff
                  </p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="serviceRequests">
              <div class="row">
                <div class="col-12">
                  <form [formGroup]="manageStaffForm">
                    <fieldset class="scheduler-border">
                      <legend></legend>
                      <div class="row">
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Porter Name<span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <input matInput placeholder="Porter Name" formControlName="staff_name" />
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="manageStaffForm.controls.staff_name"
                                [fieldName]="'Porter Name'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>

                        <div class="col-6">
                          <mat-form-field>
                            <input matInput placeholder="Employee No." id="staticEmail" formControlName="employee_no" />
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="manageStaffForm.controls.employee_no"
                                [fieldName]="'Employee No.'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Skill Level<span class="error-css">*</span>
                            </mat-label>
                            <mat-select formControlName="skill_level">
                              <mat-option disabled="true">Select Skill Level</mat-option>
                              <mat-option *ngFor="let skill of skillLevels" [value]="skill">{{ skill }}
                              </mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="manageStaffForm.controls.skill_level"
                                [fieldName]="'Skill Level'" [fieldType]="'select'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>

                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Porter Type<span class="error-css">*</span>
                            </mat-label>
                            <mat-select formControlName="staff_type" (ngModelChange)="porterTypeChange($event)">
                              <mat-option disabled>Select Porter</mat-option>
                              <mat-option [value]="'Central'">Central</mat-option>
                              <mat-option [value]="'Express'">Express</mat-option>
                              <mat-option [value]="'Station'">Station</mat-option>
                              <mat-option [value]="'Taskbased'">Taskbased</mat-option>
                              <mat-option [value]="'BusRoute'">BusRoute</mat-option>
                            </mat-select>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="manageStaffForm.controls.staff_type"
                                [fieldName]="'Porter Type'" [fieldType]="'select'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>

                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Mobile Number<span class="error-css"><span class="error-css">*</span></span>
                            </mat-label>
                            <input matInput maxlength="10" autocomplete="new-mobile_no"
                              (keydown)="inputValidation.onlyNumbers($event)" (ngModelChange)="
                                !activatedRoute.snapshot.params.id &&
                                  getMobileNumber($event)
                              " placeholder="Mobile Number" formControlName="mobile_no" />
                            <span matPrefix>+65&nbsp;</span>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="manageStaffForm.controls.mobile_no"
                                [fieldName]="'Mobile Number'" [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <input matInput placeholder="Password" [type]="fieldTextType ? 'text' : 'password'"
                              autocomplete="new-password" id="staticPassword" formControlName="password" />
                            <span class="errspan">
                              <i class="fa" [ngClass]="{
                                  'fa-eye-slash': !fieldTextType,
                                  'fa-eye': fieldTextType
                                }" (click)="toggleFieldTextType()"></i>
                            </span>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="manageStaffForm.controls.password" [fieldName]="'Password'"
                                [fieldType]="'enter'" [completeError]="
                                  'Password should be minimum 12 characters'
                                ">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>

                        <div class="col-6 pt-2">
                          <mat-radio-group formControlName="gender">
                            <mat-radio-button class="example-margin" value="male">Male
                            </mat-radio-button>
                            <mat-radio-button class="example-margin" value="female">
                              Female</mat-radio-button>
                          </mat-radio-group>
                          <mat-error class="pull-left error-css">
                            <app-error-message [control]="manageStaffForm.controls.gender" [fieldName]="'gender'"
                              [fieldType]="'select'">
                            </app-error-message>
                          </mat-error>
                        </div>

                        <div class="col-6">
                          <div class="alignInput mt-3" style="float: none">
                            <mat-radio-group formControlName="status">
                              <mat-radio-button class="example-margin" value="true">Active
                              </mat-radio-button>
                              <mat-radio-button class="example-margin" value="false">
                                Inactive</mat-radio-button>
                            </mat-radio-group>
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="manageStaffForm.controls.status" [fieldName]="'Status'"
                                [fieldType]="'select'">
                              </app-error-message>
                            </mat-error>
                          </div>
                        </div>
                        <div class="col-6">
                          <mat-form-field>
                            <mat-label>Reason
                            </mat-label>
                            <input matInput placeholder="Reason" formControlName="reason" />
                            <mat-error class="pull-left error-css">
                              <app-error-message [control]="manageStaffForm.controls.reason" [fieldName]="'Reason'"
                                [fieldType]="'enter'">
                              </app-error-message>
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div *ngIf="
                            manageStaffForm.controls.staff_type.value ==
                              'Express' ||
                            manageStaffForm.controls.staff_type.value ==
                              'Station'
                          " class="col-12">
                          <div cdkDropListGroup>
                            <div class="row">
                              <div class="col-5">
                                <div class="example-container card">
                                  <h4>Locations</h4>
                                  <div cdkDropList [cdkDropListData]="locations" class="example-list"
                                    (cdkDropListDropped)="drop($event)">
                                    <div class="example-box" *ngFor="let item of locations" cdkDrag>
                                      {{ item.location_name }}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="col-2">
                                <div class="forward-example">
                                  <p class="border-forward">></p>
                                  <p class="border-forward">>></p>
                                </div>
                                <div class="forward-example">
                                  <p class="border-forward">{{ "<" }}</p>
                                      <p class="border-forward">{{ "<<" }}</p>
                                </div>
                              </div>
                              <div class="col-5">
                                <div class="example-container card">
                                  <h4>Selected Locations</h4>
                                  <div cdkDropList [cdkDropListData]="selectedLocations" class="example-list"
                                    (cdkDropListDropped)="drop($event)">
                                    <div class="example-box" *ngFor="let item of selectedLocations" cdkDrag>
                                      {{ item.location_name }}
                                    </div>
                                  </div>
                                </div>
                                <mat-error class="pull-left error-css">
                                  <app-error-message [control]="
                                      manageStaffForm.controls.locations
                                    " [fieldName]="'Locations'" [fieldType]="'drag'">
                                  </app-error-message>
                                </mat-error>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Task based -->
                        <div *ngIf="
                            manageStaffForm.controls.staff_type.value ==
                            'Taskbased'
                          " class="col-12">
                          <div cdkDropListGroup>
                            <div class="row">
                              <div class="col-5">
                                <div class="example-container card">
                                  <h4>Job Type</h4>
                                  <div cdkDropList [cdkDropListData]="subJobList$" class="example-list"
                                    (cdkDropListDropped)="
                                      dropSubCategoryList($event)
                                    ">
                                    <div class="example-box" *ngFor="let item of subJobList$" cdkDrag>
                                      {{ item.mj_category_name }} -
                                      {{ item.sj_category_name }}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="col-2">
                                <div class="forward-example">
                                  <p class="border-forward">></p>
                                  <p class="border-forward">>></p>
                                </div>
                                <div class="forward-example">
                                  <p class="border-forward">{{ "<" }}</p>
                                      <p class="border-forward">{{ "<<" }}</p>
                                </div>
                              </div>
                              <div class="col-5">
                                <div class="example-container card">
                                  <h4>Selected Job Type</h4>
                                  <div cdkDropList [cdkDropListData]="selectedJobType" class="example-list"
                                    (cdkDropListDropped)="
                                      dropSubCategoryList($event)
                                    ">
                                    <div class="example-box" *ngFor="let item of selectedJobType" cdkDrag>
                                      {{ item.mj_category_name }} -
                                      {{ item.sj_category_name }}
                                    </div>
                                  </div>
                                </div>
                                <mat-error class="pull-left error-css">
                                  <app-error-message [control]="
                                      manageStaffForm.controls.sub_categories
                                    " [fieldName]="'Sub Job'" [fieldType]="'drag'">
                                  </app-error-message>
                                </mat-error>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- Task based -->
                        <div class="col-12">
                          <!-- <p
                                                        class="mat-form-field-hide-placeholder mat-select-placeholder col-12">
                                                        Meal Time:
                                                    </p> -->
                        </div>
                        <div class="col-6">
                          <app-time-range-picker [rangePicker]="true" [required]="false" [label]="'Meal Time One Start'"
                            [control]="
                              manageStaffForm.get('meal_time1').get('from')
                            " [fieldName]="'Meal Time One Start'" (timeChanged)="timeChanged('meal_time1')"
                            [fieldType]="'select'">
                          </app-time-range-picker>
                        </div>
                        <div class="col-6">
                          <app-time-range-picker [rangePicker]="true" [required]="false" [label]="'Meal Time One End'"
                            [control]="
                              manageStaffForm.get('meal_time1').get('to')
                            " [fieldName]="'Meal Time One End'" (timeChanged)="timeChanged('meal_time1')"
                            [fieldType]="'select'">
                          </app-time-range-picker>
                        </div>
                        <div class="col-6">
                          <app-time-range-picker [rangePicker]="true" [required]="false" [label]="'Meal Time Two Start'"
                            [control]="
                              manageStaffForm.get('meal_time2').get('from')
                            " [fieldName]="'Meal Time Two Start'" (timeChanged)="timeChanged('meal_time2')"
                            [fieldType]="'select'">
                          </app-time-range-picker>
                        </div>
                        <div class="col-6">
                          <app-time-range-picker [rangePicker]="true" [required]="false" [label]="'Meal Time Two End'"
                            [control]="
                              manageStaffForm.get('meal_time2').get('to')
                            " [fieldName]="'Meal Time Two End'" (timeChanged)="timeChanged('meal_time2')"
                            [fieldType]="'select'">
                          </app-time-range-picker>
                        </div>
                        <div class="col-6">
                          <app-time-range-picker [rangePicker]="true" [label]="'Meal Time Three Start'"
                            [required]="false" [control]="
                              manageStaffForm.get('meal_time3').get('from')
                            " [fieldName]="'Meal Time Three Start'" (timeChanged)="timeChanged('meal_time3')"
                            [fieldType]="'select'">
                          </app-time-range-picker>
                        </div>
                        <div class="col-6">
                          <app-time-range-picker [rangePicker]="true" [label]="'Meal Time Three End'" [required]="false"
                            [control]="
                              manageStaffForm.get('meal_time3').get('to')
                            " [fieldName]="'Meal Time Three End'" (timeChanged)="timeChanged('meal_time3')"
                            [fieldType]="'select'">
                          </app-time-range-picker>
                        </div>
                        <div class="col-6">
                          <app-datep-picker [dateConfiguration]="dateOfJoiningDate"
                            [control]="manageStaffForm.controls.staff_doj" [fieldName]="dateOfJoiningDate.label"
                            (getDate)="getDate()" [fieldType]="'select'">
                          </app-datep-picker>
                        </div>
                        <div class="col-6">
                          <app-datep-picker [dateConfiguration]="dateOfResignPicker"
                            [control]="manageStaffForm.controls.staff_dor" (getDate)="getDate()"
                            [fieldName]="dateOfResignPicker.label" [fieldType]="'select'">
                          </app-datep-picker>
                        </div>

                        <div class="col-3">
                          <br />
                          <div class="alignInput">
                            <mat-checkbox formControlName="EnableSA" [value]="false">
                            </mat-checkbox>
                            <mat-label>Enable SA<span class="error-css"></span>
                            </mat-label>
                          </div>
                        </div>

                        <div [class]="
                            fileupload && !fileUploaded ? 'col-6' : 'col-9'
                          ">
                          <app-file-picker [control]="manageStaffForm.controls.photo" [fieldName]="'Photo'"
                            [required]="false" [allowedExtensions]="[
                              'jpg',
                              'JPG',
                              'jpeg',
                              'JPEG',
                              'png',
                              'PNG'
                            ]" [fileupload]="fileupload" [fieldType]="'select'" (selectedFile)="getFile($event)"
                            [maxSize]="4" [showErrorRight]="true" [maxMB]="4000000">
                          </app-file-picker>

                          <mat-hint class="float-left">
                            Please upload a file < 4MB </mat-hint>
                              <mat-error class="error-css float-right" *ngIf="fileupload">
                                <div class="errormsg">
                                  <div *ngIf="fileupload && !fileUploaded">
                                    Please click on upload to upload image
                                  </div>
                                </div>
                              </mat-error>

                              <mat-error class="error-css float-right">
                                <div class="successMessage">
                                  <div *ngIf="fileUploaded">File uploaded</div>
                                </div>
                              </mat-error>
                              <span *ngIf="imageUrl">
                                <img class="image-selected" [src]="imageUrl.result" alt="image not found" />
                              </span>
                        </div>
                        <div *ngIf="fileupload && !fileUploaded" class="col-3 upload-button">
                          <button mat-raised-button type="button" (click)="uploadFile()"
                            class="btn btn-primary pull-right">
                            Upload
                          </button>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                  <div class="row from-submit">
                    <div class="col">
                      <button mat-raised-button *ngIf="!activatedRoute.snapshot.params.id"
                        class="btn btn-primary pull-right" (click)="save('save')">
                        Submit
                      </button>
                      <button mat-raised-button *ngIf="activatedRoute.snapshot.params.id"
                        class="btn btn-primary pull-right" (click)="save('update')">
                        Update
                      </button>
                      <button mat-raised-button (click)="
                          manageStaffForm.reset();
                          manageStaffForm.controls.status.setValue('true');
                          manageStaffForm.controls.gender.setValue('male');
                          getPortersById()
                        " class="btn btn-white pull-right">
                        Reset
                      </button>
                    </div>
                  </div>
                  <!-- <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                    <legend class="scheduler-border-log">
                        Edit Logs
                    </legend>
                    <div class="row">
                        <div class="col-12">
                            <label class="col-4">Edited By</label>
                            <label class="col-4">Status</label>
                            <label class="col-4">Edited On</label>
                        </div>
                    </div>
                    <div class="row" *ngFor="let log of editLogs">
                        <div class="col-4">
                            {{log.edited_by}}
                        </div>
                        <div class="col-4">
                          {{log?.approval_status == 0 ? 'Pending' : log?.approval_status == 1 ? 'Approved' : 'Rejected'}}
                        </div>
                        <div class="col-4">
                            {{log.edited_date | localDateConversion: "full"}}
                        </div>
                    </div>
                </fieldset> -->
                  <fieldset *ngIf="editLogs && editLogs.length > 0" class="scheduler-border-log">
                    <legend class="scheduler-border-log">
                      Audit Logs
                    </legend>
                    <div class="mb-8">
                      <div class="logs-header row">
                        <div class="col-2 text-center">Porter Name</div>
                        <div class="col-1 text-center">Status</div>
                        <div style="max-width: 100px;min-width: 100px;" class="text-center">Approval Status</div>
                        <div class="col-2 text-center">Request By</div>
                        <div class="col-2 text-center">Request Date</div>
                        <div class="col-2 text-center">Approved By</div>
                        <div style="max-width: 100px;min-width: 100px;" class="text-center">Approved Date</div>
                      </div>
                    </div>
                    <div class="" *ngFor="let log of editLogs; let i = index">
                      <div class="card card-xl-stretch">
                        <div class="card-body card-body-log pt-2 row m-2">
                          <div class="col-2 text-muted text-center fw-bold">{{log?.name || '--'}}</div>
                          <div class="col-1 text-muted text-center fw-bold" style="white-space: nowrap;">{{log?.status == "true" ? 'Active' :
                            'In-Active'}}</div>
                          <div style="max-width: 100px;min-width: 100px;" class="text-muted text-center fw-bold">{{log?.approvalStatus || '--'}}</div>
                          <div class="col-2 text-muted text-center fw-bold">{{log?.created_By || '--'}}
                          </div>
                          <div class="col-2 text-muted text-center fw-bold">{{log?.created_Date ?
                            (log?.formattedCreatedDate) : '--'}}</div>
                          <div class="col-2 text-muted text-center fw-bold">{{log?.approved_By || '--'}}
                          </div>
                          <div style="max-width: 100px;min-width: 100px;" class="text-muted text-center fw-bold">{{log?.approved_Date ?
                            (log?.formattedApprovedDate) : '--'}}</div>
                        </div>
                      </div>
                      <span *ngIf="i !== editLogs.length-1" style='font-size:35px;' class="text-muted">&#8593;</span>
                    </div>
                  </fieldset>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>