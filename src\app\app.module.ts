import { BrowserModule } from "@angular/platform-browser";
import { NgModule } from "@angular/core";

import { AppRoutingModule } from "./app-routing.module";
import { AppComponent } from "./app.component";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { ToastrModule } from "ngx-toastr";
import { HttpClientModule, HTTP_INTERCEPTORS } from "@angular/common/http";
import { JwtModule } from "@auth0/angular-jwt";
import { CookieService } from "ngx-cookie-service";
import { InterceptorService } from "./Apiservices/interceptors/interceptor.service";
import { BlockTemplateComponent } from "./block-ui/block-template.component";
import { WildCardRouteComponent } from "./wildCardRoute/wild-card-route/wild-card-route.component";
// import { ErrorHandle } from './errorHandler/errorhandler';
import { MatDialogModule } from "@angular/material/dialog";
// import { UserIdleModule } from 'angular-user-idle';
// import { environment } from "../environments/environment.prod";
import { BnNgIdleService } from 'bn-ng-idle';
import { BaseTimerComponent } from './shared-theme/base-timer/base-timer.component';

export function tokenGetter() {
  return localStorage.getItem("access_token");
}

@NgModule({
  declarations: [AppComponent, BlockTemplateComponent, WildCardRouteComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    MatDialogModule,
    HttpClientModule,
    ToastrModule.forRoot(),
    JwtModule.forRoot({
      config: {
        // tslint:disable-next-line:object-literal-shorthand
        tokenGetter: tokenGetter,
        // whitelistedDomains: [],
        // blacklistedRoutes: []
      },
    }),
    // UserIdleModule.forRoot({idle: environment.sessionTimeOut, timeout: 10, ping: 120})
  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: InterceptorService, multi: true },
    // { provide: ErrorHandler, useClass: ErrorHandle },
    CookieService,
    BnNgIdleService
  ],
  entryComponents: [BlockTemplateComponent],
  bootstrap: [AppComponent],
})
export class AppModule {}
