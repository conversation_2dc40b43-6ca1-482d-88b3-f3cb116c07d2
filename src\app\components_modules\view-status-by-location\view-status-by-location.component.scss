@import "../../../assets/scss/navbar.scss";

// common css list -------------
.mat-form-field {
  height: 100%;
}
img {
  vertical-align: inherit !important;
}
.expaded__panel {
  border-radius: 6px !important;
}
::ng-deep .mat-expansion-panel-header {
  background: #ececec !important;
}
.header_nav {
  color: #3f51b5 !important;
  font-weight: 600;
  font-size: 14px;
}
::ng-deep .mat-raised-button{
  height: auto !important ;
}
::ng-deep .mat-content {
  height: 34px !important;
}
.card-header {
  height: 70px !important;
}
.popoverbox {
  p {
    small {
      font-size: 14px;
    }
  }
}
.tinyBox {
  float: left;
  width: 20px;
  height: 20px;
  margin: 5px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  margin-top: 2px;
  cursor: pointer;
}

.action-button {
  background: transparent;
  box-shadow: none;
  border: none;
  cursor: pointer;
}

.popoverbox {
  border-radius: 5px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.hr-underline {
  height: 2px;
  margin-top: 3px;
  background-color: rgb(5, 5, 5);
  border: none;
}

.popover-mde {
  width: 70vw;
  height: auto;
  border: 0.5px solid rgb(104, 101, 101);
  background-color: white;
  border-radius: 3px;
  padding: 25px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.popover-mde-one {
  width: 150px;
  height: 150px;
  border: 1px solid darkblue;
  background-color: white;
}

.status-action {
  cursor: pointer;
  margin-left: 25px;
}

table td {
  color: #17123b;
  margin: 10px 0px;
  font-size: 16px;
  height: 19px;
  font-weight: 400;
  text-align: left;
  word-break: break-word;
  background-color: white;
}

.matformmodal {
  height: 60px !important;
}

::ng-deep .cdk-overlay-container {
  z-index: 99999 !important;
}

.textBreak {
  white-space: pre-wrap;
  /* CSS3 */
  white-space: -moz-pre-wrap;
  /* Firefox */
  white-space: -pre-wrap;
  /* Opera <7 */
  white-space: -o-pre-wrap;
  /* Opera 7 */
  word-wrap: break-word;
}

fieldset.scheduler-border {
  border: 0.8px groove #ddd !important;
  padding: 0 1em 1em 1em !important;
  margin: 0 0 0.5em 0 !important;
  -webkit-box-shadow: 0px 0px 0px 0px #000;
  box-shadow: 0px 0px 0px 0px #000;
}

legend.scheduler-border {
  font-weight: normal !important;
  color: darkblue;
  font-size: 13px;
  text-align: left !important;
  width: auto;
  padding: 0 10px;
  border-bottom: none;
}

legend {
  display: block;
}

.staff-image {
  height: 100px;
  width: 100px;
  border: 1px grey;
  border-radius: 3px;
}

.sttaff-image-size {
  height: 100px;
  width: 100px;
}

.btn-action {
  background: #1b2581;
}

#jobrequesttable {
  overflow: hidden;
}

.btn-default {
  background-color: #3f51b5 !important;
  // width: 98%;
  // white-space: normal !important;
}

.example-tooltip-red {
  font-size: 13px !important;
}
::ng-deep .mat-tooltip {
  font-size: 13px !important;
}

.iconsize {
  font-size: 20px;
}

.cancel__button {
  background-color: #999;
  color: #fff;
}

.job_description {
  color: #999;
  font-size: 12px;
}

.material__icon_custom {
  color: red !important;
}
