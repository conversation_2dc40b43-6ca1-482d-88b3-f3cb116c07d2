<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="card card-nav-tabs">
        <div class="card-header card-header-primary card__header__grey">
          <div class="nav-tabs-navigation">
            <div class="">
              <p style="float: right">
                <button mat-raised-button color="primary" [matMenuTriggerFor]="sub_menu_language">
                  Export
                </button>
              </p>
              <mat-menu #sub_menu_language="matMenu">
                <br />
                <span style="height: 25px" class="nav-item">
                  <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                    <p style="display: inline-block" (click)="exportTable('xsls')">
                      xsls
                    </p>
                  </a>
                  <a style="margin-top: -5px; cursor: pointer; color: #555555" class="nav-link">
                    <p style="display: inline-block" (click)="exportTable('pdf')">
                      PDF
                    </p>
                  </a>
                </span>
              </mat-menu>
              <ul class="nav">
                <li>
                  <p>Roles</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content text-center">
            <div class="tab-pane active" id="viewStatus">
              <div class="row">
                <div class="col-md-12 filter-margin"></div>
                <div class="col-md-12">
                  <div class="mat-elevation-z8" style="overflow-x: auto">
                    <table class="table" id="roleReport">
                      <thead>
                        <tr>
                          <th scope="col">Role</th>

                          <!-- <th scope="col">UETrack - Portering User Group</th>
                          <th scope="col"></th> -->
                          <th scope="col">Module Availability</th>
                        </tr>
                      </thead>
                      <tbody>
                        <!-- <tr>
                          <td>ROLE</td>
                          <td>Portering</td>
                          <td>UEMS</td>
                        </tr> -->

                        <tr *ngFor="let role of roles$ | async">
                          <td>{{ role.role_name }}</td>
                          <!-- <td>
                            <mat-icon>{{
                              role.portering ? "done" : "clear"
                            }}</mat-icon>
                          </td>
                          <td>
                            <mat-icon>{{
                              role.uems ? "done" : "clear"
                            }}</mat-icon>
                          </td> -->
                          <td> <mat-icon>{{
                              role.status ? "done" : "clear"
                              }}</mat-icon></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- <app-table-for-pdf [heads]="['ROLE', 'Portering', 'UEMS']" [title]="'Role Report'" [datas]="pdfData">
</app-table-for-pdf> -->

<app-table-for-pdf [heads]="['ROLE', 'ModuleAvailability']" [title]="'Role Report'" [datas]="pdfData">
</app-table-for-pdf>

<!-- only for xlxs donwload part only -->
<table class="table" id="roleReportDumb" style="display: none">
  <thead>
    <tr>
      <th scope="col">Role</th>
      <th scope="col">Module Availability</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let role of roles$ | async">
      <td>{{ role.role_name }}</td>
      <td> <mat-icon>{{ role.status }}</mat-icon></td>
    </tr>
  </tbody>
</table>