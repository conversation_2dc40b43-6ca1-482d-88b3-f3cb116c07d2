import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { TotalWorkLoadComponent } from "./workload/total-work-load/total-work-load.component";
import { KpiReportHomeComponent } from "./kpi-report-home/kpi-report-home.component";
import { CpJobTypeComponent } from "./workload/cp-job-type/cp-job-type.component";
import { SpJobTypeComponent } from "./workload/sp-job-type/sp-job-type.component";
import { SpWorkLoadByLocationComponent } from "./workload/sp-work-load-by-location/sp-work-load-by-location.component";
import { PerformanceKpiComponent } from "./performance-kpi/performance-kpi/performance-kpi.component";
import { TotalCancellationComponent } from "./cancellation/total-cancellation/total-cancellation.component";
import { CancellationReasonsComponent } from "./cancellation/cancellation-reasons/cancellation-reasons.component";

const routes: Routes = [
  {
    path: "",
    component: KpiReportHomeComponent,
    children: [
      {
        path: "workload",
        component: TotalWorkLoadComponent,
        data: { name: "KPI reports" },
      },
      {
        path: "cpjobtype",
        component: CpJobTypeComponent,
        data: { name: "KPI reports" },
      },
      {
        path: "spjobtype",
        component: SpJobTypeComponent,
        data: { name: "KPI reports" },
      },
      {
        path: "spworkload",
        component: SpWorkLoadByLocationComponent,
        data: { name: "KPI reports" },
      },
      {
        path: "perfomancekpi",
        component: PerformanceKpiComponent,
        data: { name: "KPI reports" },
      },
      {
        path: "totalcancellation",
        component: TotalCancellationComponent,
        data: { name: "KPI reports" },
      },
      {
        path: "cancellationreason",
        component: CancellationReasonsComponent,
        data: { name: "KPI reports" },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class KpiReportsRoutingModule {}
